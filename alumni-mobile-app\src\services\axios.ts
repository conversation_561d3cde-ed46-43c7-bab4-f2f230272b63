import axios, {
  AxiosRequestConfig,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios';

import AsyncStorage from '@react-native-async-storage/async-storage';

export const baseURL = 'http://192.168.1.90:3006/api/v1/';
// export const baseURL = 'http://34.57.197.188:3001/api/v1/';

const apiClient = axios.create({
  baseURL,
});

apiClient.interceptors.request.use(
  async (
    config: AxiosRequestConfig,
  ): Promise<InternalAxiosRequestConfig<any>> => {
    const accessToken = await AsyncStorage.getItem('accessToken');
    if (accessToken) {
      config.headers = config.headers || {};
      config.headers.Authorization = `Bearer ${accessToken}`;
    }
    return config as InternalAxiosRequestConfig<any>;
  },
  error => {
    return Promise.reject(error);
  },
);

apiClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  async error => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      // Don't try to refresh tokens for login requests
      if (originalRequest.url?.includes('/users/login')) {
        console.log(
          'Login request failed with 401, not attempting token refresh',
        );
        return Promise.reject(error);
      }

      originalRequest._retry = true;
      try {
        const refreshToken = await AsyncStorage.getItem('refreshToken');

        if (!refreshToken) {
          console.error('No refresh token found');
          // Clear all tokens and redirect to login
          await AsyncStorage.multiRemove(['accessToken', 'refreshToken']);
          return Promise.reject(new Error('No refresh token available'));
        }

        console.log('Attempting to refresh token...');
        const response = await apiClient.post('auth/refresh-token', {
          refreshToken: refreshToken,
        });

        const newAccessToken = response.data.accessToken;
        await AsyncStorage.setItem('accessToken', newAccessToken);

        // Update the original request with the new access token
        originalRequest.headers = originalRequest.headers || {};
        originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;

        console.log('Token refreshed successfully');
        // Retry the original request
        return apiClient(originalRequest);
      } catch (refreshError: any) {
        console.error('Refresh token failed:', refreshError);
        console.error('Refresh error details:', refreshError.response?.data);

        // Clear tokens on refresh failure
        await AsyncStorage.multiRemove(['accessToken', 'refreshToken']);

        // Return a more specific error
        return Promise.reject(
          new Error('Session expired. Please login again.'),
        );
      }
    }

    return Promise.reject(error);
  },
);

export default apiClient;
