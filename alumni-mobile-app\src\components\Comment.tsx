import {
  ScrollView,
  StyleSheet,
  Text,
  View,
  Image,
  Pressable,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  createComment,
  getPostComment,
  deleteComment,
} from '../services/postServices';
import moment from 'moment';
import {useSelector} from 'react-redux';
import {
  Button,
  Dialog,
  Portal,
  PaperProvider,
  DefaultTheme,
} from 'react-native-paper';
import Colors from '../constants/Colors';

interface CommentData {
  id: string;
  authorName: string;
  authorTitle: string;
  authorImage: string;
  content: string;
  timeAgo: string;
}

interface CommentProps {
  postData: any;
  fetchPosts: any;
}

const Comment: React.FC<CommentProps> = ({postData, fetchPosts}) => {
  const [comment, setComment] = useState<any>([]);
  const [commentText, setCommentText] = useState('');
  const logUserId = useSelector((state: any) => state.auth.userId);
  const userDetsils = useSelector((state: any) => state.auth.userDetails);
  const [visible, setVisible] = React.useState(false);
  const [selectedCommentId, setSelectedCommentId] = useState<any>('');

  const showDialog = (commentId: number) => {
    setSelectedCommentId(commentId);
    setVisible(true);
  };

  const hideDialog = () => {
    setVisible(false);
  };

  const fetchComments = async () => {
    try {
      const res = await getPostComment(postData.id);
      setComment(res?.comments);
    } catch (error) {
      console.error('Failed to fetch posts:', error);
    }
  };

  useEffect(() => {
    fetchComments();
  }, []);

  const handleSendComment = async () => {
    const payload = {
      postId: postData.id,
      userId: logUserId,
      commentText: commentText,
    };

    try {
      const response = await createComment(payload);
      fetchComments();
      fetchPosts();
      setCommentText('');
    } catch (error) {
      console.error('Failed to create comment:', error);
    }
  };

  const handleDelete = async () => {
    const commentId = selectedCommentId;
    const userId = logUserId;

    try {
      await deleteComment(commentId, userId);
      fetchComments();
      fetchPosts();
      hideDialog();
    } catch (error) {
      console.error('Failed to delete comment:', error);
    }
  };

  const theme = {
    ...DefaultTheme,
    myOwnProperty: true,
    colors: {
      ...DefaultTheme.colors,
      myOwnColor: '#BADA55',
    },
  };

  return (
    <SafeAreaView style={styles.container}>
      <PaperProvider theme={theme}>
        <KeyboardAvoidingView
          style={{flex: 1}}
          behavior="padding"
          keyboardVerticalOffset={30}>
          <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <ScrollView keyboardShouldPersistTaps="handled">
              <View style={styles.header}>
                <Text style={styles.headerText}>Most relevant</Text>
              </View>

              {comment && comment.length > 0 ? (
                comment.map((comment: any) => (
                  <View key={comment.id} style={styles.commentContainer}>
                    <Image
                      source={{uri: comment.userProfile}}
                      style={styles.authorImage}
                    />
                    <View style={styles.commentContent}>
                      <View style={styles.commentHeader}>
                        <View>
                          <Text style={styles.authorName}>{comment.name}</Text>
                          <Text style={styles.timeText}>
                            {moment(
                              `${comment.date} ${comment.time}`,
                              'YYYY-MM-DD hh:mm A',
                            ).fromNow()}
                          </Text>
                        </View>
                        <View style={styles.timeContainer}>
                          {logUserId === comment?.userId && (
                            <TouchableOpacity
                              onPress={() => showDialog(comment.id)}>
                              <Text style={styles.dotMenu}>•••</Text>
                            </TouchableOpacity>
                          )}
                        </View>
                      </View>
                      <Text style={styles.commentText}>
                        {comment.commentText}
                      </Text>
                    </View>
                  </View>
                ))
              ) : (
                <View style={styles.noCommentContainer}>
                  <Text style={styles.noCommentText}>No comments yet</Text>
                </View>
              )}
            </ScrollView>
          </TouchableWithoutFeedback>

          <View style={styles.commentInputContainer}>
            <Image
              source={{uri: userDetsils?.image}}
              style={styles.inputAuthorImage}
            />
            <TextInput
              placeholder="Leave a comment here..."
              placeholderTextColor={'black'}
              style={styles.commentInput}
              value={commentText}
              onChangeText={setCommentText}
            />
            <Pressable style={styles.sendButton} onPress={handleSendComment}>
              <Text style={styles.sendButtonText}>Comment</Text>
            </Pressable>
          </View>
          <Portal>
            <Dialog visible={visible} onDismiss={hideDialog}>
              <Dialog.Title style={{fontSize: 18}}>
                Delete Confirmation
              </Dialog.Title>
              <Dialog.Content>
                <Text style={{fontWeight: 'medium', color: 'red'}}>
                  Are you sure you want to delete this comment?
                </Text>
              </Dialog.Content>
              <Dialog.Actions>
                <Button onPress={hideDialog}>Cancel</Button>
                <Button onPress={handleDelete}>Delete</Button>
              </Dialog.Actions>
            </Dialog>
          </Portal>
        </KeyboardAvoidingView>
      </PaperProvider>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
  },
  commentContainer: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    color: '#666',
  },
  authorImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  commentContent: {
    flex: 1,
  },
  commentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
    color: '#666',
  },
  authorName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
  },
  authorTitle: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    color: '#666',
  },
  timeText: {
    fontSize: 12,
    color: '#666',
    marginRight: 8,
  },
  dotMenu: {
    fontSize: 16,
    color: '#666',
  },
  commentText: {
    color: '#000000',
    fontSize: 14,
    lineHeight: 20,
    marginTop: 4,
    color: '#666',
  },
  actionButtons: {
    flexDirection: 'row',
    marginTop: 8,
  },
  actionButton: {
    marginRight: 16,
  },
  actionButtonText: {
    fontSize: 14,
    color: '#666',
  },
  quickRepliesContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 8,
  },
  quickReplyButton: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  quickReplyText: {
    fontSize: 14,
    color: '#666',
  },
  commentInputContainer: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  inputAuthorImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  commentInput: {
    flex: 1,
    backgroundColor: '#f0f0f0',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    fontSize: 14,
    color: '#000000',
  },
  sendButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#0000FF',
    borderRadius: 20,
    marginLeft: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonText: {
    color: '#ffffff',
  },
  noCommentContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 60,
  },
  noCommentText: {
    fontSize: 16,
    color: '#65676b',
    fontStyle: 'italic',
  },
});

export default Comment;
