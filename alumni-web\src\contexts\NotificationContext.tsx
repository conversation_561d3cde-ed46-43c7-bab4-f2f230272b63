import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from "react";
import { io } from "socket.io-client";
import { notification } from "antd";
import notificationSound from "../Assetes/mp3/bells-notification.wav";

// Notification types
export interface NotificationData {
  id: number;
  type: string;
  title: string;
  message: string;
  data: {
    userId: number;
    traineeId: number;
    name: string;
    email: string;
    nic: string;
    contactNo: string;
    batchId: number;
    image?: string;
    registrationDate: string;
    source: "MOBILE" | "WEB";
  };
  timestamp: string;
  read: boolean;
}

interface NotificationContextType {
  notifications: NotificationData[];
  unreadCount: number;
  markAsRead: (id: number) => void;
  markAllAsRead: () => void;
  clearNotification: (id: number) => void;
  clearAllNotifications: () => void;
  isConnected: boolean;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
}) => {
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    // Initialize socket connection
    const newSocket = io("http://192.168.1.90:3007", {
      transports: ["websocket"],
      autoConnect: true,
    });

    newSocket.on("connect", () => {
      console.log("🔗 Connected to notification server");
      setIsConnected(true);
    });

    newSocket.on("disconnect", () => {
      console.log("❌ Disconnected from notification server");
      setIsConnected(false);
    });

    // Listen for new user registrations
    newSocket.on("newUserRegistration", (data: NotificationData) => {
      console.log("🔔 New user registration notification:", data);

      // Add to notifications list
      setNotifications((prev) => [data, ...prev]);

      // Show Ant Design notification
      notification.success({
        message: data.title,
        description: data.message,
        duration: 6,
        placement: "topRight",
        onClick: () => {
          // Handle notification click - could navigate to user details
          console.log("Notification clicked:", data);
        },
      });

      // Play notification sound (optional)
      try {
        const audio = new Audio(notificationSound);
        audio
          .play()
          .catch(() => console.log("Could not play notification sound"));
      } catch {
        // Ignore audio errors
      }
    });

    // Cleanup on unmount
    return () => {
      newSocket.close();
    };
  }, []);

  const markAsRead = (id: number) => {
    setNotifications((prev) =>
      prev.map((notif) => (notif.id === id ? { ...notif, read: true } : notif))
    );
  };

  const markAllAsRead = () => {
    setNotifications((prev) => prev.map((notif) => ({ ...notif, read: true })));
  };

  const clearNotification = (id: number) => {
    setNotifications((prev) => prev.filter((notif) => notif.id !== id));
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  const unreadCount = notifications.filter((notif) => !notif.read).length;

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    clearNotification,
    clearAllNotifications,
    isConnected,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error(
      "useNotifications must be used within a NotificationProvider"
    );
  }
  return context;
};
