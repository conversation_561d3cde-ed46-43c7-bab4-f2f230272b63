{"ast": null, "code": "export const baseUrl = \"http://192.168.1.49:3006/api/v1/\";\n// export const baseUrl = \"http://34.57.197.188:3001/api/v1/\";", "map": {"version": 3, "names": ["baseUrl"], "sources": ["C:/Users/<USER>/Desktop/sg-alumni/alumni-web/src/API/index.tsx"], "sourcesContent": ["export const baseUrl = \"http://192.168.1.49:3006/api/v1/\";\r\n// export const baseUrl = \"http://34.57.197.188:3001/api/v1/\";\r\n"], "mappings": "AAAA,OAAO,MAAMA,OAAO,GAAG,kCAAkC;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}