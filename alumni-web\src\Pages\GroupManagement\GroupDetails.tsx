import React, { useState, useEffect } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Typography,
  Input,
  Modal,
  Form,
  Select,
  message,
  Popconfirm,
  Tag,
  Avatar,
  Row,
  Col,
  Statistic,
  Tabs,
  DatePicker,
  List,
  Tooltip,
  Badge,
  Dropdown,
  Menu,
} from "antd";
import {
  ArrowLeftOutlined,
  UserAddOutlined,
  UserOutlined,
  UserDeleteOutlined,
  MessageOutlined,
  DeleteOutlined,
  SearchOutlined,
  TeamOutlined,
  CalendarOutlined,
  ExclamationCircleOutlined,
  EditOutlined,
  SendOutlined,
  StopOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined,
  UnlockOutlined,
} from "@ant-design/icons";
import { useParams, useNavigate } from "react-router-dom";
import { apiService } from "../../services/apiService";
import { io } from "socket.io-client";
import "./GroupManagement.css";

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

interface GroupMember {
  id: string;
  userId: string;
  user: {
    id: string;
    username: string;
    email: string;
    image: string;
    role?: string;
  };
  joinedAt: string;
  isBlocked?: boolean;
  blockedBy?: string;
  blockedAt?: string;
}

interface Message {
  id: string;
  content: string;
  userId: string;
  user: {
    id: string;
    username: string;
    email: string;
    image: string;
  };
  createdAt: string;
  updatedAt: string;
  images?: Array<{ imageUrl: string }>;
  isDeleted?: boolean;
  deletedBy?: {
    userId: string;
    role: string;
    isAdmin: boolean;
  };
  deletedAt?: string;
}

interface User {
  id: string;
  username: string;
  email: string;
  image: string;
  role?: string;
}

const GroupDetails: React.FC = () => {
  const { groupId } = useParams<{ groupId: string }>();
  const navigate = useNavigate();

  const [group, setGroup] = useState<any>(null);
  const [members, setMembers] = useState<GroupMember[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [allUsers, setAllUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState("");
  const [isAddMemberModalVisible, setIsAddMemberModalVisible] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState("members");
  const [isExternalUserModalVisible, setIsExternalUserModalVisible] =
    useState(false);
  const [externalUsers, setExternalUsers] = useState<User[]>([]);
  const [selectedExternalUsers, setSelectedExternalUsers] = useState<string[]>(
    []
  );

  // Message deletion states
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteType, setDeleteType] = useState<
    "single" | "all" | "user" | "dateRange"
  >("single");
  const [selectedMessageId, setSelectedMessageId] = useState<string>("");
  const [selectedUserId, setSelectedUserId] = useState<string>("");
  const [dateRange, setDateRange] = useState<[any, any] | null>(null);
  const [deletingMessages, setDeletingMessages] = useState(false);
  const [socket, setSocket] = useState<any>(null);

  useEffect(() => {
    if (groupId) {
      fetchGroupDetails();
      fetchGroupMembers();
      fetchGroupMessages();
      fetchAvailableUsers();
    }
  }, [groupId]);

  // Socket connection for real-time message deletion updates
  useEffect(() => {
    if (!groupId) return;

    const newSocket = io("http://192.168.1.90:3008", {
      transports: ["websocket"],
      autoConnect: true,
    });

    newSocket.on("connect", () => {
      console.log("🔗 Connected to message server");
      newSocket.emit("joinGroup", groupId);
    });

    // Listen for single message deletion
    newSocket.on("messageDeleted", (deletionData: any) => {
      console.log("📧 Message deleted:", deletionData);
      setMessages((prevMessages) =>
        prevMessages.map((msg) =>
          msg.id === deletionData.id.toString()
            ? {
                ...msg,
                isDeleted: true,
                deletedBy: deletionData.deletedBy,
                deletedAt: deletionData.deletedAt,
              }
            : msg
        )
      );
    });

    // Listen for bulk message deletion
    newSocket.on("messagesBulkDeleted", (bulkDeletionData: any) => {
      console.log("📧 Bulk messages deleted:", bulkDeletionData);

      if (bulkDeletionData.type === "bulk_delete_all") {
        // Mark all messages as deleted
        setMessages((prevMessages) =>
          prevMessages.map((msg) => ({
            ...msg,
            isDeleted: true,
            deletedBy: bulkDeletionData.deletedBy,
            deletedAt: bulkDeletionData.deletedAt,
          }))
        );
      } else {
        // Mark specific messages as deleted
        setMessages((prevMessages) =>
          prevMessages.map((msg) =>
            bulkDeletionData.deletedMessageIds.includes(parseInt(msg.id))
              ? {
                  ...msg,
                  isDeleted: true,
                  deletedBy: bulkDeletionData.deletedBy,
                  deletedAt: bulkDeletionData.deletedAt,
                }
              : msg
          )
        );
      }
    });

    setSocket(newSocket);

    return () => {
      newSocket.disconnect();
    };
  }, [groupId]);

  const fetchGroupDetails = async () => {
    try {
      const response = await apiService.getGroupById(groupId!);
      if (response.data.success) {
        setGroup(response.data.group);
      }
    } catch (error) {
      console.error("Error fetching group details:", error);
      message.error("Failed to fetch group details");
    }
  };

  const fetchGroupMembers = async () => {
    setLoading(true);
    try {
      const response = await apiService.getGroupMembers(groupId!);
      if (response.data.success) {
        setMembers(response.data.members || []);
      }
    } catch (error) {
      console.error("Error fetching group members:", error);
      message.error("Failed to fetch group members");
    } finally {
      setLoading(false);
    }
  };

  const fetchGroupMessages = async () => {
    setLoading(true);
    try {
      const response = await apiService.getGroupMessages(groupId!);
      console.log(response);

      if (response) {
        setMessages(response.data || []);
      }
    } catch (error) {
      console.error("Error fetching group messages:", error);
      message.error("Failed to fetch group messages");
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailableUsers = async () => {
    if (!groupId) return;

    try {
      console.log("🔍 Fetching available users for group:", groupId);
      const response = await apiService.getAvailableUsersForGroup(groupId);
      if (response.data.success) {
        console.log("👥 Available users:", response.data.users);
        console.log("📋 Group info:", response.data.groupInfo);
        setAllUsers(response.data.users || []);
      }
    } catch (error) {
      console.error("Error fetching available users:", error);
      message.error("Failed to fetch available users");
    }
  };

  const handleAddMembers = async () => {
    try {
      const promises = selectedUsers.map((userId) =>
        apiService.addGroupMember({ userId, groupId: groupId! })
      );

      await Promise.all(promises);
      message.success(`${selectedUsers.length} member(s) added successfully`);
      setIsAddMemberModalVisible(false);
      setSelectedUsers([]);
      fetchGroupMembers();
    } catch (error) {
      console.error("Error adding members:", error);
      message.error("Failed to add members");
    }
  };

  const handleRemoveMember = async (userId: string) => {
    try {
      await apiService.removeGroupMember(userId, groupId!);
      message.success("Member removed successfully");
      fetchGroupMembers();
    } catch (error) {
      console.error("Error removing member:", error);
      message.error("Failed to remove member");
    }
  };

  const handleAddExternalUser = async () => {
    if (!groupId) return;

    try {
      // Fetch all users for external selection
      const response = await apiService.getAllUsers();
      if (response.data.success) {
        const currentMemberIds = members.map((m) => m.id);
        const groupBatchIds = group?.batches?.map((b: any) => b.id) || [];

        // Filter to show only external users (not in group's batches and not current members)
        const availableExternalUsers = response.data.users.filter(
          (user: any) => {
            // Exclude current members
            if (currentMemberIds.includes(user.id)) return false;

            // If group has specific batches, only show users NOT from those batches
            if (groupBatchIds.length > 0) {
              return (
                !user.trainee?.batchId ||
                !groupBatchIds.includes(user.trainee.batchId)
              );
            }

            // If group is universal, show all non-members
            return true;
          }
        );

        setExternalUsers(availableExternalUsers);
        setSelectedExternalUsers([]);
        setIsExternalUserModalVisible(true);
      }
    } catch (error) {
      console.error("Error fetching external users:", error);
      message.error("Failed to fetch external users");
    }
  };

  const handleSaveExternalUsers = async () => {
    if (!groupId || selectedExternalUsers.length === 0) return;

    try {
      const promises = selectedExternalUsers.map((userId) =>
        apiService.addGroupMember({ userId, groupId: groupId! })
      );

      await Promise.all(promises);
      message.success(
        `${selectedExternalUsers.length} external user(s) added successfully`
      );
      setIsExternalUserModalVisible(false);
      setSelectedExternalUsers([]);
      fetchGroupMembers();
    } catch (error) {
      console.error("Error adding external users:", error);
      message.error("Failed to add external users");
    }
  };

  const handleBlockMember = async (userId: string) => {
    try {
      await apiService.blockGroupMember(userId, groupId!);
      message.success("Member blocked from messaging successfully");
      fetchGroupMembers();
    } catch (error) {
      console.error("Error blocking member:", error);
      message.error("Failed to block member");
    }
  };

  const handleUnblockMember = async (userId: string) => {
    try {
      await apiService.unblockGroupMember(userId, groupId!);
      message.success("Member unblocked successfully");
      fetchGroupMembers();
    } catch (error) {
      console.error("Error unblocking member:", error);
      message.error("Failed to unblock member");
    }
  };

  const handleDeleteMessage = async (messageId: string) => {
    try {
      await apiService.deleteMessage(messageId);
      message.success("Message deleted successfully");
      fetchGroupMessages();
    } catch (error) {
      console.error("Error deleting message:", error);
      message.error("Failed to delete message");
    }
  };

  const handleDeleteAllMessages = async () => {
    try {
      await apiService.deleteAllGroupMessages(groupId!);
      message.success("All messages deleted successfully");
      fetchGroupMessages();
    } catch (error) {
      console.error("Error deleting all messages:", error);
      message.error("Failed to delete all messages");
    }
  };

  const handleDeleteMessagesByDateRange = async (
    startDate: string,
    endDate: string
  ) => {
    try {
      await apiService.deleteGroupMessagesByDateRange(
        groupId!,
        startDate,
        endDate
      );
      message.success("Messages deleted successfully");
      fetchGroupMessages();
    } catch (error) {
      console.error("Error deleting messages by date range:", error);
      message.error("Failed to delete messages");
    }
  };

  // New functions for enhanced message deletion
  const handleDeleteUserMessages = async (userId: string) => {
    try {
      setDeletingMessages(true);
      await apiService.deleteAllUserMessages(userId);
      message.success("All user messages deleted successfully");
      fetchGroupMessages();
    } catch (error) {
      console.error("Error deleting user messages:", error);
      message.error("Failed to delete user messages");
    } finally {
      setDeletingMessages(false);
    }
  };

  const handleDeleteUserMessagesByDateRange = async (
    userId: string,
    startDate: string,
    endDate: string
  ) => {
    try {
      setDeletingMessages(true);
      await apiService.deleteUserMessagesByDateRange(
        userId,
        startDate,
        endDate
      );
      message.success("User messages in date range deleted successfully");
      fetchGroupMessages();
    } catch (error) {
      console.error("Error deleting user messages by date range:", error);
      message.error("Failed to delete user messages");
    } finally {
      setDeletingMessages(false);
    }
  };

  const openDeleteModal = (
    type: "single" | "all" | "user" | "dateRange",
    messageId?: string,
    userId?: string
  ) => {
    setDeleteType(type);
    setSelectedMessageId(messageId || "");
    setSelectedUserId(userId || "");
    setDeleteModalVisible(true);
  };

  const handleConfirmDelete = async () => {
    if (deleteType === "single" && selectedMessageId) {
      await handleDeleteMessage(selectedMessageId);
    } else if (deleteType === "all") {
      await handleDeleteAllMessages();
    } else if (deleteType === "user" && selectedUserId) {
      await handleDeleteUserMessages(selectedUserId);
    } else if (deleteType === "dateRange" && dateRange) {
      const startDate = dateRange[0].format("YYYY-MM-DD");
      const endDate = dateRange[1].format("YYYY-MM-DD");

      if (selectedUserId) {
        await handleDeleteUserMessagesByDateRange(
          selectedUserId,
          startDate,
          endDate
        );
      } else {
        await handleDeleteMessagesByDateRange(startDate, endDate);
      }
    }

    setDeleteModalVisible(false);
    setDateRange(null);
    setSelectedUserId("");
    setSelectedMessageId("");
  };

  const availableUsers = allUsers.filter(
    (user) =>
      !members.some((member) => member.userId === user.id) &&
      user.username?.toLowerCase().includes(searchText.toLowerCase())
  );

  const filteredMessages = messages.filter(
    (message) =>
      message.content?.toLowerCase().includes(searchText.toLowerCase()) ||
      message.user?.username?.toLowerCase().includes(searchText.toLowerCase())
  );

  const memberColumns = [
    {
      title: "Member",
      dataIndex: "user",
      key: "user",
      render: (user: any) => (
        <Space>
          <Avatar src={user?.image} icon={<TeamOutlined />} />
          <div>
            <Text strong>{user?.username || "Unknown User"}</Text>
            <br />
            <Text type="secondary">{user?.email || "No email"}</Text>
          </div>
        </Space>
      ),
    },
    {
      title: "Joined",
      dataIndex: "joinedAt",
      key: "joinedAt",
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: "Status",
      key: "status",
      render: (_: any, record: GroupMember) => (
        <Space>
          {record.isBlocked ? (
            <Tag color="red" icon={<StopOutlined />}>
              Blocked
            </Tag>
          ) : (
            <Tag color="green" icon={<CheckCircleOutlined />}>
              Active
            </Tag>
          )}
          {record.blockedAt && (
            <Tooltip
              title={`Blocked on ${new Date(
                record.blockedAt
              ).toLocaleString()}`}
            >
              <InfoCircleOutlined />
            </Tooltip>
          )}
        </Space>
      ),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_: any, record: GroupMember) => (
        <Space>
          {record.isBlocked ? (
            <Popconfirm
              title="Are you sure you want to unblock this member? They will be able to send messages again."
              onConfirm={() => handleUnblockMember(record.userId)}
              okText="Yes"
              cancelText="No"
            >
              <Button type="primary" icon={<UnlockOutlined />} size="small">
                Unblock
              </Button>
            </Popconfirm>
          ) : (
            <Popconfirm
              title="Are you sure you want to block this member? They will not be able to send messages in this group."
              onConfirm={() => handleBlockMember(record.userId)}
              okText="Yes"
              cancelText="No"
            >
              <Button icon={<StopOutlined />} size="small">
                Block
              </Button>
            </Popconfirm>
          )}
          <Popconfirm
            title="Are you sure you want to remove this member from the group? This action cannot be undone."
            onConfirm={() => handleRemoveMember(record.userId)}
            okText="Yes"
            cancelText="No"
          >
            <Button danger icon={<UserDeleteOutlined />} size="small">
              Remove
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const messageColumns = [
    {
      title: "Message",
      dataIndex: "content",
      key: "content",
      render: (content: string, record: Message) => (
        <div>
          <Space>
            <Avatar src={record.user?.image} size="small" />
            <Text strong>{record.user?.username || "Unknown User"}</Text>
            <Text type="secondary" style={{ fontSize: "12px" }}>
              {new Date(record.createdAt).toLocaleString()}
            </Text>
            {record.isDeleted && (
              <Tag color="red" icon={<DeleteOutlined />}>
                Deleted
              </Tag>
            )}
          </Space>
          <div style={{ marginTop: 8 }}>
            {record.isDeleted ? (
              <div
                style={{
                  padding: "8px 12px",
                  backgroundColor: "#fff2f0",
                  border: "1px solid #ffccc7",
                  borderRadius: "6px",
                  fontStyle: "italic",
                }}
              >
                <Text type="secondary" style={{ color: "#cf1322" }}>
                  <DeleteOutlined style={{ marginRight: 8 }} />
                  {record.deletedBy?.isAdmin
                    ? "This message was deleted by admin"
                    : "This message was deleted by user"}
                </Text>
                {record.deletedAt && (
                  <div style={{ marginTop: 4 }}>
                    <Text
                      type="secondary"
                      style={{ fontSize: "11px", color: "#8c8c8c" }}
                    >
                      Deleted on {new Date(record.deletedAt).toLocaleString()}
                    </Text>
                  </div>
                )}
              </div>
            ) : (
              <>
                <Text>{content || "No content"}</Text>
                {record.images && record.images.length > 0 && (
                  <div style={{ marginTop: 8 }}>
                    {record.images.map((img, index) => (
                      <img
                        key={index}
                        src={img.imageUrl}
                        alt="Message attachment"
                        style={{
                          maxWidth: 100,
                          maxHeight: 100,
                          marginRight: 8,
                        }}
                      />
                    ))}
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      ),
    },
    {
      title: "Actions",
      key: "actions",
      render: (text: any, record: Message) => {
        // Don't show actions for deleted messages
        if (record.isDeleted) {
          return (
            <Text type="secondary" style={{ fontStyle: "italic" }}>
              No actions available
            </Text>
          );
        }

        const deleteMenu = (
          <Menu>
            <Menu.Item
              key="single"
              icon={<DeleteOutlined />}
              onClick={() => openDeleteModal("single", record.id)}
            >
              Delete This Message
            </Menu.Item>
            <Menu.Item
              key="user"
              icon={<UserDeleteOutlined />}
              onClick={() => openDeleteModal("user", undefined, record.user.id)}
            >
              Delete All Messages by {record.user?.username || "Unknown User"}
            </Menu.Item>
            <Menu.Divider />
            <Menu.Item
              key="userDateRange"
              icon={<CalendarOutlined />}
              onClick={() =>
                openDeleteModal("dateRange", undefined, record.user.id)
              }
            >
              Delete User Messages by Date Range
            </Menu.Item>
          </Menu>
        );

        return (
          <Space>
            <Popconfirm
              title="Are you sure you want to delete this message? This action cannot be undone."
              onConfirm={() => handleDeleteMessage(record.id)}
              okText="Yes"
              cancelText="No"
            >
              <Button danger icon={<DeleteOutlined />} size="small">
                Delete
              </Button>
            </Popconfirm>
            <Dropdown
              menu={{ items: deleteMenu.props.children }}
              trigger={["click"]}
            >
              <Button size="small" icon={<DeleteOutlined />}>
                More Options
              </Button>
            </Dropdown>
          </Space>
        );
      },
    },
  ];

  if (!group) {
    return <div>Loading...</div>;
  }

  return (
    <div className="group-management-container">
      {/* Header */}
      <Card style={{ marginBottom: 24 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={() => navigate("/group-management")}
              >
                Back to Group Forums
              </Button>
              <Avatar src={group.image} icon={<TeamOutlined />} size="large" />
              <div>
                <Title level={3} style={{ margin: 0 }}>
                  {group.name}
                </Title>
                <Text type="secondary">Group Forum Management</Text>
              </div>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Statistics */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="Total Members"
              value={members.length}
              prefix={<TeamOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="Total Messages"
              value={messages.length}
              prefix={<MessageOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="Created"
              value={new Date(group.createdAt).toLocaleDateString()}
              prefix={<CalendarOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* Main Content */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={`Members (${members.length})`} key="members">
            {/* Member Statistics */}
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Card size="small">
                  <Statistic
                    title="Total Members"
                    value={members.length}
                    prefix={<TeamOutlined />}
                    valueStyle={{ color: "#1890ff" }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small">
                  <Statistic
                    title="Available Users"
                    value={availableUsers.length}
                    prefix={<UserAddOutlined />}
                    valueStyle={{ color: "#52c41a" }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small">
                  <Statistic
                    title="Admin Members"
                    value={
                      members.filter((member) => member.user?.role === "ADMIN")
                        .length
                    }
                    prefix={<TeamOutlined />}
                    valueStyle={{ color: "#f5222d" }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small">
                  <Statistic
                    title="HR Members"
                    value={
                      members.filter((member) => member.user?.role === "HR")
                        .length
                    }
                    prefix={<TeamOutlined />}
                    valueStyle={{ color: "#fa8c16" }}
                  />
                </Card>
              </Col>
            </Row>

            {/* Blocked Members Statistics */}
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={8}>
                <Card size="small">
                  <Statistic
                    title="Blocked Members"
                    value={members.filter((member) => member.isBlocked).length}
                    prefix={<StopOutlined />}
                    valueStyle={{ color: "#f5222d" }}
                  />
                </Card>
              </Col>
              <Col span={8}>
                <Card size="small">
                  <Statistic
                    title="Active Members"
                    value={members.filter((member) => !member.isBlocked).length}
                    prefix={<CheckCircleOutlined />}
                    valueStyle={{ color: "#52c41a" }}
                  />
                </Card>
              </Col>
              <Col span={8}>
                <Card size="small">
                  <Statistic
                    title="Block Rate"
                    value={
                      members.length > 0
                        ? Math.round(
                            (members.filter((member) => member.isBlocked)
                              .length /
                              members.length) *
                              100
                          )
                        : 0
                    }
                    suffix="%"
                    prefix={<InfoCircleOutlined />}
                    valueStyle={{ color: "#722ed1" }}
                  />
                </Card>
              </Col>
            </Row>

            <div style={{ marginBottom: 16 }}>
              <Row justify="space-between" align="middle">
                <Col>
                  <Search
                    placeholder="Search members..."
                    allowClear
                    style={{ width: 300 }}
                    prefix={<SearchOutlined />}
                  />
                </Col>
                <Col>
                  <Space>
                    <Text type="secondary">
                      Showing {members.length} of{" "}
                      {members.length + availableUsers.length} total users
                    </Text>
                    <Button
                      type="primary"
                      icon={<UserAddOutlined />}
                      onClick={() => setIsAddMemberModalVisible(true)}
                    >
                      Add Members
                    </Button>
                    <Button
                      type="default"
                      icon={<UserOutlined />}
                      onClick={handleAddExternalUser}
                    >
                      Add External User
                    </Button>
                  </Space>
                </Col>
              </Row>
            </div>

            <Table
              columns={memberColumns}
              dataSource={members}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
              scroll={{ x: "max-content" }}
              size="middle"
            />
          </TabPane>

          <TabPane tab={`Messages (${messages.length})`} key="messages">
            <div style={{ marginBottom: 16 }}>
              <Row justify="space-between" align="middle">
                <Col>
                  <Search
                    placeholder="Search messages..."
                    allowClear
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    style={{ width: 300 }}
                    prefix={<SearchOutlined />}
                  />
                </Col>
                <Col>
                  <Space>
                    <Popconfirm
                      title="Are you sure you want to delete all messages? This will permanently delete all messages in this group and cannot be undone."
                      onConfirm={handleDeleteAllMessages}
                      okText="Yes"
                      cancelText="No"
                    >
                      <Button danger icon={<DeleteOutlined />}>
                        Delete All Messages
                      </Button>
                    </Popconfirm>

                    <Button
                      icon={<CalendarOutlined />}
                      onClick={() => openDeleteModal("dateRange")}
                    >
                      Delete by Date Range
                    </Button>
                  </Space>
                </Col>
              </Row>
            </div>

            <Table
              columns={messageColumns}
              dataSource={filteredMessages}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
              scroll={{ x: "max-content" }}
              size="middle"
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* Add Members Modal */}
      <Modal
        title="Add Members to Group"
        open={isAddMemberModalVisible}
        onCancel={() => setIsAddMemberModalVisible(false)}
        onOk={handleAddMembers}
        okText="Add Selected Members"
        width={600}
      >
        <div style={{ marginBottom: 16 }}>
          <Search
            placeholder="Search users..."
            allowClear
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: "100%" }}
          />
        </div>

        <Select
          mode="multiple"
          placeholder="Select users to add"
          value={selectedUsers}
          onChange={setSelectedUsers}
          style={{ width: "100%", marginBottom: 16 }}
          showSearch
          filterOption={(input, option) => {
            const children = option?.children;
            if (!children) return false;
            return children
              .toString()
              .toLowerCase()
              .includes(input?.toLowerCase() || "");
          }}
        >
          {availableUsers.map((user) => (
            <Option key={user.id} value={user.id}>
              <Space>
                <Avatar src={user?.image} size="small" />
                {user?.username || "Unknown User"} ({user?.email || "No email"})
              </Space>
            </Option>
          ))}
        </Select>

        <Text type="secondary">{selectedUsers.length} user(s) selected</Text>
      </Modal>

      {/* Enhanced Delete Modal */}
      <Modal
        title="Delete Messages"
        open={deleteModalVisible}
        onCancel={() => {
          setDeleteModalVisible(false);
          setDateRange(null);
          setSelectedUserId("");
          setSelectedMessageId("");
        }}
        onOk={handleConfirmDelete}
        confirmLoading={deletingMessages}
        okText="Delete"
        okButtonProps={{ danger: true }}
      >
        {deleteType === "single" && (
          <p>
            Are you sure you want to delete this message? This action cannot be
            undone.
          </p>
        )}

        {deleteType === "all" && (
          <p>
            Are you sure you want to delete ALL messages in this group? This
            will permanently delete all messages and cannot be undone.
          </p>
        )}

        {deleteType === "user" && (
          <p>
            Are you sure you want to delete ALL messages by this user in this
            group? This action cannot be undone.
          </p>
        )}

        {deleteType === "dateRange" && (
          <div>
            <p>Select a date range to delete messages:</p>
            <DatePicker.RangePicker
              value={dateRange}
              onChange={setDateRange}
              style={{ width: "100%", marginBottom: 16 }}
              placeholder={["Start Date", "End Date"]}
            />

            {selectedUserId && (
              <p>
                <strong>Note:</strong> This will delete messages by the selected
                user within the specified date range.
              </p>
            )}

            {!selectedUserId && (
              <p>
                <strong>Note:</strong> This will delete ALL messages in the
                group within the specified date range.
              </p>
            )}
          </div>
        )}
      </Modal>

      {/* External User Modal */}
      <Modal
        title={`Add External Users to ${group?.name || "Group"}`}
        open={isExternalUserModalVisible}
        onCancel={() => {
          setIsExternalUserModalVisible(false);
          setSelectedExternalUsers([]);
        }}
        onOk={handleSaveExternalUsers}
        okText="Add Selected Users"
        okButtonProps={{ disabled: selectedExternalUsers.length === 0 }}
        width={600}
      >
        <div style={{ marginBottom: 16 }}>
          <Text type="secondary">
            <InfoCircleOutlined style={{ marginRight: 8 }} />
            External users are users who don't belong to this group's assigned
            batches but can still access the group.
          </Text>
        </div>

        {group && group.batches && group.batches.length > 0 && (
          <div
            style={{
              marginBottom: 16,
              padding: 12,
              backgroundColor: "#f0f8ff",
              borderRadius: 6,
              border: "1px solid #d6f7ff",
            }}
          >
            <Text strong style={{ fontSize: 13 }}>
              Group Batches:{" "}
              {group.batches.map((b: any) => b.batchNo).join(", ")}
            </Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              Showing users from OTHER batches or users without batch
              assignments
            </Text>
          </div>
        )}

        <Select
          mode="multiple"
          placeholder="Select external users to add"
          style={{ width: "100%" }}
          value={selectedExternalUsers}
          onChange={setSelectedExternalUsers}
          showSearch
          filterOption={(input, option) =>
            (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
          }
          options={externalUsers.map((user) => ({
            value: user.id.toString(),
            label: `${user.username} (${user.email}) - ${user.role}`,
          }))}
        />

        {selectedExternalUsers.length > 0 && (
          <div
            style={{
              marginTop: 16,
              padding: 12,
              backgroundColor: "#f6ffed",
              borderRadius: 6,
              border: "1px solid #b7eb8f",
            }}
          >
            <Text strong style={{ color: "#52c41a" }}>
              {selectedExternalUsers.length} user(s) selected for addition
            </Text>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default GroupDetails;
