# ninja log v5
12	97	0	C:/Users/<USER>/Desktop/sg-alumni/alumni-mobile-app/node_modules/react-native-reanimated/android/.cxx/Debug/46416659/x86/CMakeFiles/cmake.verify_globs	87f5d7c934afcc8e
5	5399	7750367219306258	src/main/cpp/worklets/CMakeFiles/worklets.dir/baa02494a34db5a33303ebf2c9592326/Common/cpp/worklets/Tools/JSScheduler.cpp.o	f1e687330109f851
356	6099	7750367227057604	src/main/cpp/worklets/CMakeFiles/worklets.dir/f96306655a2392a68b7cb0310303e3e7/cpp/worklets/Tools/JSISerializer.cpp.o	6daca9d2baa9a0f5
806	6853	7750367229767599	src/main/cpp/worklets/CMakeFiles/worklets.dir/baa02494a34db5a33303ebf2c9592326/Common/cpp/worklets/Tools/JSLogger.cpp.o	8e65d1cb31bcfa8e
1456	7922	7750367251890999	src/main/cpp/worklets/CMakeFiles/worklets.dir/7af6c96b14aea9d29e7758a49d1fa273/NativeModules/WorkletsModuleProxy.cpp.o	de3f355e3990f886
1933	8617	7750367246530394	src/main/cpp/worklets/CMakeFiles/worklets.dir/7af6c96b14aea9d29e7758a49d1fa273/NativeModules/WorkletsModuleProxySpec.cpp.o	2e3f7272bfdeaad5
2333	9143	7750367260047680	src/main/cpp/worklets/CMakeFiles/worklets.dir/7af6c96b14aea9d29e7758a49d1fa273/Registries/EventHandlerRegistry.cpp.o	ce926882a9878f27
2796	9720	7750367250235457	src/main/cpp/worklets/CMakeFiles/worklets.dir/baa02494a34db5a33303ebf2c9592326/Common/cpp/worklets/Tools/AsyncQueue.cpp.o	36baaa40b4c227e1
3487	10235	7750367276040668	src/main/cpp/worklets/CMakeFiles/worklets.dir/f96306655a2392a68b7cb0310303e3e7/cpp/worklets/SharedItems/Shareables.cpp.o	f86693f247302bc
4397	10916	7750367261875957	src/main/cpp/worklets/CMakeFiles/worklets.dir/7af6c96b14aea9d29e7758a49d1fa273/Registries/WorkletRuntimeRegistry.cpp.o	8a144ff0579fb64f
4867	11655	7750367271755654	src/main/cpp/worklets/CMakeFiles/worklets.dir/f96306655a2392a68b7cb0310303e3e7/cpp/worklets/Tools/ReanimatedJSIUtils.cpp.o	6456a6bc6346c48f
5422	12440	7750367309607960	src/main/cpp/worklets/CMakeFiles/worklets.dir/f96306655a2392a68b7cb0310303e3e7/cpp/worklets/Tools/ReanimatedVersion.cpp.o	dd84fd02b8471874
6100	13001	7750367288319738	src/main/cpp/worklets/CMakeFiles/worklets.dir/31ffcf47bca7cd3078890f62099c8b49/worklets/Tools/WorkletEventHandler.cpp.o	3a5e2d302e92b2ed
6864	13755	7750367293315056	src/main/cpp/worklets/CMakeFiles/worklets.dir/baa02494a34db5a33303ebf2c9592326/Common/cpp/worklets/Tools/UIScheduler.cpp.o	ba960a51ae6c5082
7925	14103	7750367310037949	src/main/cpp/worklets/CMakeFiles/worklets.dir/904e03e0566c29472b31fa74f311e2a9/RNRuntimeWorkletDecorator.cpp.o	2bf19e5439c7ffde
8618	14678	7750367325473364	src/main/cpp/worklets/CMakeFiles/worklets.dir/7af6c96b14aea9d29e7758a49d1fa273/WorkletRuntime/WorkletRuntime.cpp.o	5425d60d14007b13
9144	15202	7750367324485833	src/main/cpp/worklets/CMakeFiles/worklets.dir/904e03e0566c29472b31fa74f311e2a9/ReanimatedHermesRuntime.cpp.o	9159714c7c5a9b0e
9722	15703	7750367323235850	src/main/cpp/worklets/CMakeFiles/worklets.dir/7af6c96b14aea9d29e7758a49d1fa273/WorkletRuntime/ReanimatedRuntime.cpp.o	a3af8eb7f4ab8b3f
10917	16205	7750367324125838	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/PlatformLogger.cpp.o	21d2e34ccb281d99
13757	16693	7750367329032569	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/31ffcf47bca7cd3078890f62099c8b49/reanimated/Fabric/PropsRegistry.cpp.o	9f0450de99036814
14104	17222	7750367334940917	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/cafd7f2cc3a3d5689f72655791390dd5/Fabric/ReanimatedCommitHook.cpp.o	9586192995c81e9
15203	17659	7750367345462652	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/14dbe95b6c3b02a5b0515666739d2a4d/LayoutAnimationsProxy.cpp.o	eaadb201ba3371d8
16206	18385	7750367355369028	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/14dbe95b6c3b02a5b0515666739d2a4d/LayoutAnimationsUtils.cpp.o	a0db09fda6c8ff84
16694	19104	7750367360309040	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/cafd7f2cc3a3d5689f72655791390dd5/Fabric/ShadowTreeCloner.cpp.o	afde5f068623f7c3
10236	19609	7750367357909043	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/AndroidUIScheduler.cpp.o	a6f62eb6cf80ecaa
17223	20055	7750367364593560	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/cafd7f2cc3a3d5689f72655791390dd5/Fabric/ReanimatedMountHook.cpp.o	25b6a38fb7181b63
14681	20592	7750367375185002	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/b83d658e1dcd98633c6ea0382c7a8424/AnimatedSensorModule.cpp.o	9e348c840d626ae9
11656	21057	7750367376581881	src/main/cpp/worklets/CMakeFiles/worklets.dir/904e03e0566c29472b31fa74f311e2a9/WorkletRuntimeDecorator.cpp.o	3b2f6dd9f8256df0
19105	21540	7750367401282614	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/31ffcf47bca7cd3078890f62099c8b49/reanimated/Tools/FeaturesConfig.cpp.o	cd41385655dec00e
12450	21978	7750367379945918	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsOnLoad.cpp.o	c1bd311a523bb082
13003	22800	7750367392943942	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsModule.cpp.o	66429144936f5c2a
15705	23902	7750367394631521	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/14dbe95b6c3b02a5b0515666739d2a4d/LayoutAnimationsManager.cpp.o	507250adccf17fda
18386	25485	7750367439972744	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/2c92e9484f829ce332f57444920e1bf5/UIRuntimeDecorator.cpp.o	ae4b2d9119ee362d
20593	25705	7750367443412708	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/2c92e9484f829ce332f57444920e1bf5/RNRuntimeDecorator.cpp.o	8037a9a9b99ae41d
21058	26458	7750367450483637	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/820de9f96c4f5fc4c28878888c15441a/ReanimatedModuleProxySpec.cpp.o	900d748efb22e0c0
22801	27177	7750367457255543	../../../../build/intermediates/cxx/Debug/46416659/obj/x86/libworklets.so	1cba131da0e7bb32
17660	27232	7750367458549269	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/820de9f96c4f5fc4c28878888c15441a/ReanimatedModuleProxy.cpp.o	c2c48067d2be9c2
19614	27316	7750367459769275	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/JNIHelper.cpp.o	46c797727e9b591
20056	27474	7750367460509239	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/LayoutAnimations.cpp.o	8cd298f2f341a3ab
21541	29777	7750367484588084	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/OnLoad.cpp.o	64567d1d4d812e2d
21979	33146	7750367518057327	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/NativeProxy.cpp.o	2f8f96c867a1c24f
33147	34515	7750367531898835	../../../../build/intermediates/cxx/Debug/46416659/obj/x86/libreanimated.so	aba997f04623b7c3
11	106	0	C:/Users/<USER>/Desktop/sg-alumni/alumni-mobile-app/node_modules/react-native-reanimated/android/.cxx/Debug/46416659/x86/CMakeFiles/cmake.verify_globs	87f5d7c934afcc8e
10	256	0	C:/Users/<USER>/Desktop/sg-alumni/alumni-mobile-app/node_modules/react-native-reanimated/android/.cxx/Debug/46416659/x86/CMakeFiles/cmake.verify_globs	87f5d7c934afcc8e
20	184	0	C:/Users/<USER>/Desktop/sg-alumni/alumni-mobile-app/node_modules/react-native-reanimated/android/.cxx/Debug/46416659/x86/CMakeFiles/cmake.verify_globs	87f5d7c934afcc8e
7	96	0	C:/Users/<USER>/Desktop/sg-alumni/alumni-mobile-app/node_modules/react-native-reanimated/android/.cxx/Debug/46416659/x86/CMakeFiles/cmake.verify_globs	87f5d7c934afcc8e
