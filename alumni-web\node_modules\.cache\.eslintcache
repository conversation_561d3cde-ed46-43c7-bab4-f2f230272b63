[{"C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Routes\\Routes.tsx": "4", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\Trainee\\Trainee.tsx": "5", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\Group\\Group.tsx": "6", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\Promotions\\Promotions.tsx": "7", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\Batch\\Batch.tsx": "8", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\LayOuts\\SimpleLayout\\SimpleLayout.tsx": "9", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Components\\Auth\\Login.tsx": "10", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Components\\Header\\Header.tsx": "11", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Components\\SideBar\\SideBar.tsx": "12", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\API\\index.tsx": "13", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\MasterData\\MasterData.tsx": "14", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\MasterData\\Countries\\Countries.tsx": "15", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\MasterData\\MobileConfig\\MobileConfig.tsx": "16", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\MasterData\\Companies\\Companies.tsx": "17", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\MasterData\\Skills\\Skills.tsx": "18", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\MasterData\\Batches\\Batches.tsx": "19", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\MasterData\\Groups\\Groups.tsx": "20", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\TraineeManagement\\TraineeManagement.tsx": "21", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\VerifyAccount\\VerifyAccount.tsx": "22", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\contexts\\NotificationContext.tsx": "23", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Components\\NotificationBell\\NotificationBell.tsx": "24", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\contexts\\AuthContext.tsx": "25", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\utils\\roleUtils.ts": "26", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\services\\apiService.ts": "27", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Components\\ProtectedRoute.tsx": "28", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\ResetPassword\\ResetPassword.tsx": "29", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\ForgotPassword\\ForgotPassword.tsx": "30", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\AppShare\\AppShare.tsx": "31", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\GroupManagement\\GroupManagement.tsx": "32", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\GroupManagement\\GroupDetails.tsx": "33", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Components\\ErrorBoundary\\ErrorBoundary.tsx": "34", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Components\\ErrorBoundary\\index.ts": "35", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\Permissions\\MobilePermissions.tsx": "36", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Components\\MobilePermissions\\index.ts": "37", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Components\\MobilePermissions\\PermissionExamples.tsx": "38", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Components\\MobilePermissions\\MobilePermissionsDisplay.tsx": "39", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Components\\MobilePermissions\\EditablePermissionsDisplay.tsx": "40", "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\services\\permissionService.ts": "41"}, {"size": 1179, "mtime": 1752576241338, "results": "42", "hashOfConfig": "43"}, {"size": 440, "mtime": 1736138039888, "results": "44", "hashOfConfig": "43"}, {"size": 1257, "mtime": 1752578169905, "results": "45", "hashOfConfig": "43"}, {"size": 5600, "mtime": 1752818489289, "results": "46", "hashOfConfig": "43"}, {"size": 42427, "mtime": 1753335717960, "results": "47", "hashOfConfig": "43"}, {"size": 6866, "mtime": 1751989546562, "results": "48", "hashOfConfig": "43"}, {"size": 16479, "mtime": 1752742719999, "results": "49", "hashOfConfig": "43"}, {"size": 8120, "mtime": 1753094553663, "results": "50", "hashOfConfig": "43"}, {"size": 742, "mtime": 1737119574482, "results": "51", "hashOfConfig": "43"}, {"size": 3337, "mtime": 1752389327172, "results": "52", "hashOfConfig": "43"}, {"size": 2049, "mtime": 1751966904760, "results": "53", "hashOfConfig": "43"}, {"size": 4271, "mtime": 1753098502648, "results": "54", "hashOfConfig": "43"}, {"size": 124, "mtime": 1753696769457, "results": "55", "hashOfConfig": "43"}, {"size": 7770, "mtime": 1750930346467, "results": "56", "hashOfConfig": "43"}, {"size": 10084, "mtime": 1751988157375, "results": "57", "hashOfConfig": "43"}, {"size": 14656, "mtime": 1750928988781, "results": "58", "hashOfConfig": "43"}, {"size": 15828, "mtime": 1751974892851, "results": "59", "hashOfConfig": "43"}, {"size": 11916, "mtime": 1751975009465, "results": "60", "hashOfConfig": "43"}, {"size": 16569, "mtime": 1752810504912, "results": "61", "hashOfConfig": "43"}, {"size": 26846, "mtime": 1753093916905, "results": "62", "hashOfConfig": "43"}, {"size": 16379, "mtime": 1751989085678, "results": "63", "hashOfConfig": "43"}, {"size": 3564, "mtime": 1751989143805, "results": "64", "hashOfConfig": "43"}, {"size": 3884, "mtime": 1753696769444, "results": "65", "hashOfConfig": "43"}, {"size": 5810, "mtime": 1751879005643, "results": "66", "hashOfConfig": "43"}, {"size": 1759, "mtime": 1751966393627, "results": "67", "hashOfConfig": "43"}, {"size": 12439, "mtime": 1752814545876, "results": "68", "hashOfConfig": "43"}, {"size": 9249, "mtime": 1753097052286, "results": "69", "hashOfConfig": "43"}, {"size": 1615, "mtime": 1752810697963, "results": "70", "hashOfConfig": "43"}, {"size": 9089, "mtime": 1752391070521, "results": "71", "hashOfConfig": "43"}, {"size": 6192, "mtime": 1752390418002, "results": "72", "hashOfConfig": "43"}, {"size": 9207, "mtime": 1752396855732, "results": "73", "hashOfConfig": "43"}, {"size": 12797, "mtime": 1753094868305, "results": "74", "hashOfConfig": "43"}, {"size": 36256, "mtime": 1753696769905, "results": "75", "hashOfConfig": "43"}, {"size": 2651, "mtime": 1752578152717, "results": "76", "hashOfConfig": "43"}, {"size": 43, "mtime": 1752578162371, "results": "77", "hashOfConfig": "43"}, {"size": 16179, "mtime": 1752829367699, "results": "78", "hashOfConfig": "43"}, {"size": 238, "mtime": 1752819311045, "results": "79", "hashOfConfig": "43"}, {"size": 4460, "mtime": 1752814598926, "results": "80", "hashOfConfig": "43"}, {"size": 5041, "mtime": 1752817496801, "results": "81", "hashOfConfig": "43"}, {"size": 6899, "mtime": 1752819300544, "results": "82", "hashOfConfig": "43"}, {"size": 4976, "mtime": 1752829178794, "results": "83", "hashOfConfig": "43"}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "8x98t8", {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\App.tsx", ["207", "208"], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Routes\\Routes.tsx", ["209", "210"], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\Trainee\\Trainee.tsx", ["211", "212", "213", "214", "215", "216", "217", "218", "219"], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\Group\\Group.tsx", ["220", "221", "222"], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\Promotions\\Promotions.tsx", ["223", "224", "225", "226", "227", "228", "229", "230", "231", "232"], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\Batch\\Batch.tsx", ["233", "234", "235", "236", "237", "238", "239", "240", "241", "242", "243", "244", "245", "246", "247", "248", "249", "250"], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\LayOuts\\SimpleLayout\\SimpleLayout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Components\\Auth\\Login.tsx", ["251", "252"], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Components\\Header\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Components\\SideBar\\SideBar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\API\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\MasterData\\MasterData.tsx", ["253"], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\MasterData\\Countries\\Countries.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\MasterData\\MobileConfig\\MobileConfig.tsx", ["254", "255", "256", "257"], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\MasterData\\Companies\\Companies.tsx", ["258"], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\MasterData\\Skills\\Skills.tsx", ["259", "260", "261"], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\MasterData\\Batches\\Batches.tsx", ["262", "263"], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\MasterData\\Groups\\Groups.tsx", ["264", "265", "266", "267", "268"], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\TraineeManagement\\TraineeManagement.tsx", ["269", "270"], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\VerifyAccount\\VerifyAccount.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\contexts\\NotificationContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Components\\NotificationBell\\NotificationBell.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\utils\\roleUtils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\services\\apiService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\ResetPassword\\ResetPassword.tsx", ["271", "272"], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\ForgotPassword\\ForgotPassword.tsx", ["273", "274"], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\AppShare\\AppShare.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\GroupManagement\\GroupManagement.tsx", ["275"], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\GroupManagement\\GroupDetails.tsx", ["276", "277", "278", "279", "280", "281", "282", "283", "284"], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Components\\ErrorBoundary\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Components\\ErrorBoundary\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Pages\\Permissions\\MobilePermissions.tsx", ["285", "286", "287", "288"], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Components\\MobilePermissions\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Components\\MobilePermissions\\PermissionExamples.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Components\\MobilePermissions\\MobilePermissionsDisplay.tsx", ["289"], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\Components\\MobilePermissions\\EditablePermissionsDisplay.tsx", ["290"], [], "C:\\Users\\<USER>\\Desktop\\sg-alumni\\alumni-web\\src\\services\\permissionService.ts", ["291"], [], {"ruleId": "292", "severity": 1, "message": "293", "line": 4, "column": 8, "nodeType": "294", "messageId": "295", "endLine": 4, "endColumn": 17}, {"ruleId": "292", "severity": 1, "message": "296", "line": 9, "column": 8, "nodeType": "294", "messageId": "295", "endLine": 9, "endColumn": 25}, {"ruleId": "292", "severity": 1, "message": "297", "line": 27, "column": 10, "nodeType": "294", "messageId": "295", "endLine": 27, "endColumn": 25}, {"ruleId": "298", "severity": 1, "message": "299", "line": 35, "column": 6, "nodeType": "300", "endLine": 35, "endColumn": 8, "suggestions": "301"}, {"ruleId": "292", "severity": 1, "message": "302", "line": 15, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 15, "endColumn": 11}, {"ruleId": "292", "severity": 1, "message": "303", "line": 21, "column": 27, "nodeType": "294", "messageId": "295", "endLine": 21, "endColumn": 47}, {"ruleId": "292", "severity": 1, "message": "304", "line": 30, "column": 8, "nodeType": "294", "messageId": "295", "endLine": 30, "endColumn": 14}, {"ruleId": "292", "severity": 1, "message": "305", "line": 69, "column": 11, "nodeType": "294", "messageId": "295", "endLine": 69, "endColumn": 19}, {"ruleId": "292", "severity": 1, "message": "306", "line": 79, "column": 11, "nodeType": "294", "messageId": "295", "endLine": 79, "endColumn": 22}, {"ruleId": "292", "severity": 1, "message": "307", "line": 88, "column": 10, "nodeType": "294", "messageId": "295", "endLine": 88, "endColumn": 24}, {"ruleId": "292", "severity": 1, "message": "308", "line": 88, "column": 26, "nodeType": "294", "messageId": "295", "endLine": 88, "endColumn": 43}, {"ruleId": "292", "severity": 1, "message": "309", "line": 89, "column": 10, "nodeType": "294", "messageId": "295", "endLine": 89, "endColumn": 17}, {"ruleId": "292", "severity": 1, "message": "310", "line": 89, "column": 19, "nodeType": "294", "messageId": "295", "endLine": 89, "endColumn": 29}, {"ruleId": "292", "severity": 1, "message": "311", "line": 14, "column": 8, "nodeType": "294", "messageId": "295", "endLine": 14, "endColumn": 13}, {"ruleId": "292", "severity": 1, "message": "312", "line": 14, "column": 17, "nodeType": "294", "messageId": "295", "endLine": 14, "endColumn": 22}, {"ruleId": "313", "severity": 1, "message": "314", "line": 157, "column": 5, "nodeType": "315", "messageId": "316", "endLine": 157, "endColumn": 26}, {"ruleId": "292", "severity": 1, "message": "302", "line": 14, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 14, "endColumn": 11}, {"ruleId": "292", "severity": 1, "message": "303", "line": 20, "column": 10, "nodeType": "294", "messageId": "295", "endLine": 20, "endColumn": 30}, {"ruleId": "292", "severity": 1, "message": "311", "line": 21, "column": 8, "nodeType": "294", "messageId": "295", "endLine": 21, "endColumn": 13}, {"ruleId": "292", "severity": 1, "message": "312", "line": 21, "column": 17, "nodeType": "294", "messageId": "295", "endLine": 21, "endColumn": 22}, {"ruleId": "292", "severity": 1, "message": "317", "line": 26, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 26, "endColumn": 22}, {"ruleId": "292", "severity": 1, "message": "318", "line": 31, "column": 11, "nodeType": "294", "messageId": "295", "endLine": 31, "endColumn": 19}, {"ruleId": "292", "severity": 1, "message": "319", "line": 36, "column": 11, "nodeType": "294", "messageId": "295", "endLine": 36, "endColumn": 16}, {"ruleId": "292", "severity": 1, "message": "320", "line": 61, "column": 10, "nodeType": "294", "messageId": "295", "endLine": 61, "endColumn": 16}, {"ruleId": "292", "severity": 1, "message": "321", "line": 61, "column": 18, "nodeType": "294", "messageId": "295", "endLine": 61, "endColumn": 27}, {"ruleId": "313", "severity": 1, "message": "314", "line": 370, "column": 5, "nodeType": "315", "messageId": "316", "endLine": 370, "endColumn": 26}, {"ruleId": "292", "severity": 1, "message": "322", "line": 7, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 7, "endColumn": 14}, {"ruleId": "292", "severity": 1, "message": "302", "line": 13, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 13, "endColumn": 11}, {"ruleId": "292", "severity": 1, "message": "323", "line": 14, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 14, "endColumn": 9}, {"ruleId": "292", "severity": 1, "message": "303", "line": 19, "column": 10, "nodeType": "294", "messageId": "295", "endLine": 19, "endColumn": 30}, {"ruleId": "292", "severity": 1, "message": "312", "line": 20, "column": 17, "nodeType": "294", "messageId": "295", "endLine": 20, "endColumn": 22}, {"ruleId": "292", "severity": 1, "message": "324", "line": 23, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 23, "endColumn": 17}, {"ruleId": "292", "severity": 1, "message": "317", "line": 25, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 25, "endColumn": 22}, {"ruleId": "292", "severity": 1, "message": "325", "line": 26, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 26, "endColumn": 15}, {"ruleId": "292", "severity": 1, "message": "304", "line": 28, "column": 8, "nodeType": "294", "messageId": "295", "endLine": 28, "endColumn": 14}, {"ruleId": "292", "severity": 1, "message": "318", "line": 30, "column": 11, "nodeType": "294", "messageId": "295", "endLine": 30, "endColumn": 19}, {"ruleId": "292", "severity": 1, "message": "319", "line": 35, "column": 11, "nodeType": "294", "messageId": "295", "endLine": 35, "endColumn": 16}, {"ruleId": "326", "severity": 1, "message": "327", "line": 50, "column": 7, "nodeType": "294", "messageId": "328", "endLine": 50, "endColumn": 22}, {"ruleId": "292", "severity": 1, "message": "329", "line": 56, "column": 10, "nodeType": "294", "messageId": "295", "endLine": 56, "endColumn": 14}, {"ruleId": "292", "severity": 1, "message": "330", "line": 57, "column": 10, "nodeType": "294", "messageId": "295", "endLine": 57, "endColumn": 17}, {"ruleId": "292", "severity": 1, "message": "320", "line": 58, "column": 10, "nodeType": "294", "messageId": "295", "endLine": 58, "endColumn": 16}, {"ruleId": "292", "severity": 1, "message": "321", "line": 58, "column": 18, "nodeType": "294", "messageId": "295", "endLine": 58, "endColumn": 27}, {"ruleId": "292", "severity": 1, "message": "331", "line": 146, "column": 9, "nodeType": "294", "messageId": "295", "endLine": 146, "endColumn": 27}, {"ruleId": "292", "severity": 1, "message": "332", "line": 202, "column": 9, "nodeType": "294", "messageId": "295", "endLine": 202, "endColumn": 17}, {"ruleId": "292", "severity": 1, "message": "333", "line": 3, "column": 18, "nodeType": "294", "messageId": "295", "endLine": 3, "endColumn": 26}, {"ruleId": "292", "severity": 1, "message": "334", "line": 4, "column": 15, "nodeType": "294", "messageId": "295", "endLine": 4, "endColumn": 27}, {"ruleId": "292", "severity": 1, "message": "335", "line": 4, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 4, "endColumn": 19}, {"ruleId": "292", "severity": 1, "message": "323", "line": 15, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 15, "endColumn": 9}, {"ruleId": "292", "severity": 1, "message": "336", "line": 24, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 24, "endColumn": 17}, {"ruleId": "326", "severity": 1, "message": "337", "line": 58, "column": 7, "nodeType": "294", "messageId": "328", "endLine": 58, "endColumn": 29}, {"ruleId": "298", "severity": 1, "message": "338", "line": 65, "column": 6, "nodeType": "300", "endLine": 65, "endColumn": 8, "suggestions": "339"}, {"ruleId": "292", "severity": 1, "message": "340", "line": 17, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 17, "endColumn": 10}, {"ruleId": "292", "severity": 1, "message": "340", "line": 17, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 17, "endColumn": 10}, {"ruleId": "292", "severity": 1, "message": "341", "line": 18, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 18, "endColumn": 8}, {"ruleId": "292", "severity": 1, "message": "342", "line": 26, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 26, "endColumn": 15}, {"ruleId": "292", "severity": 1, "message": "343", "line": 34, "column": 9, "nodeType": "294", "messageId": "295", "endLine": 34, "endColumn": 16}, {"ruleId": "298", "severity": 1, "message": "344", "line": 78, "column": 6, "nodeType": "300", "endLine": 78, "endColumn": 8, "suggestions": "345"}, {"ruleId": "292", "severity": 1, "message": "346", "line": 17, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 17, "endColumn": 7}, {"ruleId": "292", "severity": 1, "message": "340", "line": 18, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 18, "endColumn": 10}, {"ruleId": "292", "severity": 1, "message": "343", "line": 38, "column": 9, "nodeType": "294", "messageId": "295", "endLine": 38, "endColumn": 16}, {"ruleId": "292", "severity": 1, "message": "347", "line": 98, "column": 10, "nodeType": "294", "messageId": "295", "endLine": 98, "endColumn": 20}, {"ruleId": "298", "severity": 1, "message": "348", "line": 115, "column": 6, "nodeType": "300", "endLine": 115, "endColumn": 8, "suggestions": "349"}, {"ruleId": "292", "severity": 1, "message": "350", "line": 11, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 11, "endColumn": 8}, {"ruleId": "292", "severity": 1, "message": "351", "line": 19, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 19, "endColumn": 8}, {"ruleId": "292", "severity": 1, "message": "341", "line": 10, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 10, "endColumn": 8}, {"ruleId": "298", "severity": 1, "message": "352", "line": 52, "column": 6, "nodeType": "300", "endLine": 52, "endColumn": 23, "suggestions": "353"}, {"ruleId": "292", "severity": 1, "message": "354", "line": 2, "column": 58, "nodeType": "294", "messageId": "295", "endLine": 2, "endColumn": 62}, {"ruleId": "292", "severity": 1, "message": "355", "line": 20, "column": 9, "nodeType": "294", "messageId": "295", "endLine": 20, "endColumn": 17}, {"ruleId": "292", "severity": 1, "message": "356", "line": 100, "column": 13, "nodeType": "294", "messageId": "295", "endLine": 100, "endColumn": 33}, {"ruleId": "292", "severity": 1, "message": "357", "line": 10, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 10, "endColumn": 7}, {"ruleId": "292", "severity": 1, "message": "346", "line": 21, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 21, "endColumn": 7}, {"ruleId": "292", "severity": 1, "message": "358", "line": 23, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 23, "endColumn": 8}, {"ruleId": "292", "severity": 1, "message": "359", "line": 37, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 37, "endColumn": 28}, {"ruleId": "292", "severity": 1, "message": "360", "line": 38, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 38, "endColumn": 15}, {"ruleId": "292", "severity": 1, "message": "361", "line": 39, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 39, "endColumn": 15}, {"ruleId": "292", "severity": 1, "message": "306", "line": 53, "column": 9, "nodeType": "294", "messageId": "295", "endLine": 53, "endColumn": 20}, {"ruleId": "292", "severity": 1, "message": "362", "line": 131, "column": 10, "nodeType": "294", "messageId": "295", "endLine": 131, "endColumn": 16}, {"ruleId": "298", "severity": 1, "message": "363", "line": 140, "column": 6, "nodeType": "300", "endLine": 140, "endColumn": 15, "suggestions": "364"}, {"ruleId": "292", "severity": 1, "message": "333", "line": 14, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 14, "endColumn": 11}, {"ruleId": "292", "severity": 1, "message": "365", "line": 15, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 15, "endColumn": 6}, {"ruleId": "292", "severity": 1, "message": "366", "line": 27, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 27, "endColumn": 27}, {"ruleId": "292", "severity": 1, "message": "367", "line": 36, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 36, "endColumn": 27}, {"ruleId": "292", "severity": 1, "message": "368", "line": 7, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 7, "endColumn": 19}, {"ruleId": "292", "severity": 1, "message": "367", "line": 7, "column": 3, "nodeType": "294", "messageId": "295", "endLine": 7, "endColumn": 27}, {"ruleId": "369", "severity": 1, "message": "370", "line": 163, "column": 1, "nodeType": "371", "endLine": 163, "endColumn": 40}, "@typescript-eslint/no-unused-vars", "'LoginPage' is defined but never used.", "Identifier", "unusedVar", "'notificationSound' is defined but never used.", "'isAuthenticated' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'token'. Either include it or remove the dependency array.", "ArrayExpression", ["372"], "'Timeline' is defined but never used.", "'AiOutlineCloseCircle' is defined but never used.", "'moment' is defined but never used.", "'FormData' is defined but never used.", "'RangePicker' is assigned a value but never used.", "'showExperience' is assigned a value but never used.", "'setShowExperience' is assigned a value but never used.", "'stackId' is assigned a value but never used.", "'setStackId' is assigned a value but never used.", "'dayjs' is defined but never used.", "'Dayjs' is defined but never used.", "no-unreachable", "Unreachable code.", "ExpressionStatement", "unreachableCode", "'MinusCircleOutlined' is defined but never used.", "'Position' is defined but never used.", "'Stack' is defined but never used.", "'values' is assigned a value but never used.", "'setValues' is assigned a value but never used.", "'InputNumber' is defined but never used.", "'Upload' is defined but never used.", "'DeleteOutlined' is defined but never used.", "'PlusOutlined' is defined but never used.", "@typescript-eslint/no-redeclare", "'Batch' is already defined.", "redeclared", "'imgs' is assigned a value but never used.", "'batchNo' is assigned a value but never used.", "'handleNotification' is assigned a value but never used.", "'normFile' is assigned a value but never used.", "'Checkbox' is defined but never used.", "'FormInstance' is defined but never used.", "'DatabaseOutlined' is defined but never used.", "'UploadOutlined' is defined but never used.", "'MobileConfig' is already defined.", "React Hook useEffect has a missing dependency: 'fetchConfig'. Either include it or remove the dependency array.", ["373"], "'Divider' is defined but never used.", "'Alert' is defined but never used.", "'StarOutlined' is defined but never used.", "'TabPane' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchBatches'. Either include it or remove the dependency array.", ["374"], "'List' is defined but never used.", "'memberForm' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchGroups'. Either include it or remove the dependency array.", ["375"], "'Space' is defined but never used.", "'Image' is defined but never used.", "React Hook useEffect has a missing dependency: 'verifyToken'. Either include it or remove the dependency array.", ["376"], "'Spin' is defined but never used.", "'navigate' is assigned a value but never used.", "'mockBatches' is assigned a value but never used.", "'Form' is defined but never used.", "'Badge' is defined but never used.", "'ExclamationCircleOutlined' is defined but never used.", "'EditOutlined' is defined but never used.", "'SendOutlined' is defined but never used.", "'socket' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchAvailableUsers', 'fetchGroupDetails', 'fetchGroupMembers', and 'fetchGroupMessages'. Either include them or remove the dependency array.", ["377"], "'Tag' is defined but never used.", "'MobilePermissionsDisplay' is defined but never used.", "'getPermissionDescription' is defined but never used.", "'ROLE_PERMISSIONS' is defined but never used.", "import/no-anonymous-default-export", "Assign instance to a variable before exporting as module default", "ExportDefaultDeclaration", {"desc": "378", "fix": "379"}, {"desc": "380", "fix": "381"}, {"desc": "382", "fix": "383"}, {"desc": "384", "fix": "385"}, {"desc": "386", "fix": "387"}, {"desc": "388", "fix": "389"}, "Update the dependencies array to be: [token]", {"range": "390", "text": "391"}, "Update the dependencies array to be: [fetchConfig]", {"range": "392", "text": "393"}, "Update the dependencies array to be: [fetchBatches]", {"range": "394", "text": "395"}, "Update the dependencies array to be: [fetchGroups]", {"range": "396", "text": "397"}, "Update the dependencies array to be: [token, navigate, verifyToken]", {"range": "398", "text": "399"}, "Update the dependencies array to be: [fetchAvailableUsers, fetchGroupDetails, fetchGroupMembers, fetchGroupMessages, groupId]", {"range": "400", "text": "401"}, [1752, 1754], "[token]", [1407, 1409], "[fetchConfig]", [1688, 1690], "[fetchBatches]", [2636, 2638], "[fetchGroups]", [1310, 1327], "[token, navigate, verifyToken]", [3332, 3341], "[fetchAvailableUsers, fetchGroupDetails, fetchGroupMembers, fetchGroupMessages, groupId]"]