{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\sg-alumni\\\\alumni-web\\\\src\\\\contexts\\\\NotificationContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from \"react\";\nimport { io } from \"socket.io-client\";\nimport { notification } from \"antd\";\nimport notificationSound from \"../Assetes/mp3/bells-notification.wav\";\n\n// Notification types\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NotificationContext = /*#__PURE__*/createContext(undefined);\nexport const NotificationProvider = ({\n  children\n}) => {\n  _s();\n  const [notifications, setNotifications] = useState([]);\n  const [isConnected, setIsConnected] = useState(false);\n  useEffect(() => {\n    // Initialize socket connection\n    const newSocket = io(\"http://192.168.1.90:3007\", {\n      transports: [\"websocket\"],\n      autoConnect: true\n    });\n    newSocket.on(\"connect\", () => {\n      console.log(\"🔗 Connected to notification server\");\n      setIsConnected(true);\n    });\n    newSocket.on(\"disconnect\", () => {\n      console.log(\"❌ Disconnected from notification server\");\n      setIsConnected(false);\n    });\n\n    // Listen for new user registrations\n    newSocket.on(\"newUserRegistration\", data => {\n      console.log(\"🔔 New user registration notification:\", data);\n\n      // Add to notifications list\n      setNotifications(prev => [data, ...prev]);\n\n      // Show Ant Design notification\n      notification.success({\n        message: data.title,\n        description: data.message,\n        duration: 6,\n        placement: \"topRight\",\n        onClick: () => {\n          // Handle notification click - could navigate to user details\n          console.log(\"Notification clicked:\", data);\n        }\n      });\n\n      // Play notification sound (optional)\n      try {\n        const audio = new Audio(notificationSound);\n        audio.play().catch(() => console.log(\"Could not play notification sound\"));\n      } catch {\n        // Ignore audio errors\n      }\n    });\n\n    // Cleanup on unmount\n    return () => {\n      newSocket.close();\n    };\n  }, []);\n  const markAsRead = id => {\n    setNotifications(prev => prev.map(notif => notif.id === id ? {\n      ...notif,\n      read: true\n    } : notif));\n  };\n  const markAllAsRead = () => {\n    setNotifications(prev => prev.map(notif => ({\n      ...notif,\n      read: true\n    })));\n  };\n  const clearNotification = id => {\n    setNotifications(prev => prev.filter(notif => notif.id !== id));\n  };\n  const clearAllNotifications = () => {\n    setNotifications([]);\n  };\n  const unreadCount = notifications.filter(notif => !notif.read).length;\n  const value = {\n    notifications,\n    unreadCount,\n    markAsRead,\n    markAllAsRead,\n    clearNotification,\n    clearAllNotifications,\n    isConnected\n  };\n  return /*#__PURE__*/_jsxDEV(NotificationContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 5\n  }, this);\n};\n_s(NotificationProvider, \"aWewdq25ED2A5mFDXswcF4iNJ9k=\");\n_c = NotificationProvider;\nexport const useNotifications = () => {\n  _s2();\n  const context = useContext(NotificationContext);\n  if (context === undefined) {\n    throw new Error(\"useNotifications must be used within a NotificationProvider\");\n  }\n  return context;\n};\n_s2(useNotifications, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"NotificationProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "io", "notification", "notificationSound", "jsxDEV", "_jsxDEV", "NotificationContext", "undefined", "NotificationProvider", "children", "_s", "notifications", "setNotifications", "isConnected", "setIsConnected", "newSocket", "transports", "autoConnect", "on", "console", "log", "data", "prev", "success", "message", "title", "description", "duration", "placement", "onClick", "audio", "Audio", "play", "catch", "close", "mark<PERSON><PERSON><PERSON>", "id", "map", "notif", "read", "markAllAsRead", "clearNotification", "filter", "clearAllNotifications", "unreadCount", "length", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useNotifications", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/sg-alumni/alumni-web/src/contexts/NotificationContext.tsx"], "sourcesContent": ["import React, {\n  createContext,\n  useContext,\n  useEffect,\n  useState,\n  ReactNode,\n} from \"react\";\nimport { io } from \"socket.io-client\";\nimport { notification } from \"antd\";\nimport notificationSound from \"../Assetes/mp3/bells-notification.wav\";\n\n// Notification types\nexport interface NotificationData {\n  id: number;\n  type: string;\n  title: string;\n  message: string;\n  data: {\n    userId: number;\n    traineeId: number;\n    name: string;\n    email: string;\n    nic: string;\n    contactNo: string;\n    batchId: number;\n    image?: string;\n    registrationDate: string;\n    source: \"MOBILE\" | \"WEB\";\n  };\n  timestamp: string;\n  read: boolean;\n}\n\ninterface NotificationContextType {\n  notifications: NotificationData[];\n  unreadCount: number;\n  markAsRead: (id: number) => void;\n  markAllAsRead: () => void;\n  clearNotification: (id: number) => void;\n  clearAllNotifications: () => void;\n  isConnected: boolean;\n}\n\nconst NotificationContext = createContext<NotificationContextType | undefined>(\n  undefined\n);\n\ninterface NotificationProviderProps {\n  children: ReactNode;\n}\n\nexport const NotificationProvider: React.FC<NotificationProviderProps> = ({\n  children,\n}) => {\n  const [notifications, setNotifications] = useState<NotificationData[]>([]);\n  const [isConnected, setIsConnected] = useState(false);\n\n  useEffect(() => {\n    // Initialize socket connection\n    const newSocket = io(\"http://192.168.1.90:3007\", {\n      transports: [\"websocket\"],\n      autoConnect: true,\n    });\n\n    newSocket.on(\"connect\", () => {\n      console.log(\"🔗 Connected to notification server\");\n      setIsConnected(true);\n    });\n\n    newSocket.on(\"disconnect\", () => {\n      console.log(\"❌ Disconnected from notification server\");\n      setIsConnected(false);\n    });\n\n    // Listen for new user registrations\n    newSocket.on(\"newUserRegistration\", (data: NotificationData) => {\n      console.log(\"🔔 New user registration notification:\", data);\n\n      // Add to notifications list\n      setNotifications((prev) => [data, ...prev]);\n\n      // Show Ant Design notification\n      notification.success({\n        message: data.title,\n        description: data.message,\n        duration: 6,\n        placement: \"topRight\",\n        onClick: () => {\n          // Handle notification click - could navigate to user details\n          console.log(\"Notification clicked:\", data);\n        },\n      });\n\n      // Play notification sound (optional)\n      try {\n        const audio = new Audio(notificationSound);\n        audio\n          .play()\n          .catch(() => console.log(\"Could not play notification sound\"));\n      } catch {\n        // Ignore audio errors\n      }\n    });\n\n    // Cleanup on unmount\n    return () => {\n      newSocket.close();\n    };\n  }, []);\n\n  const markAsRead = (id: number) => {\n    setNotifications((prev) =>\n      prev.map((notif) => (notif.id === id ? { ...notif, read: true } : notif))\n    );\n  };\n\n  const markAllAsRead = () => {\n    setNotifications((prev) => prev.map((notif) => ({ ...notif, read: true })));\n  };\n\n  const clearNotification = (id: number) => {\n    setNotifications((prev) => prev.filter((notif) => notif.id !== id));\n  };\n\n  const clearAllNotifications = () => {\n    setNotifications([]);\n  };\n\n  const unreadCount = notifications.filter((notif) => !notif.read).length;\n\n  const value: NotificationContextType = {\n    notifications,\n    unreadCount,\n    markAsRead,\n    markAllAsRead,\n    clearNotification,\n    clearAllNotifications,\n    isConnected,\n  };\n\n  return (\n    <NotificationContext.Provider value={value}>\n      {children}\n    </NotificationContext.Provider>\n  );\n};\n\nexport const useNotifications = (): NotificationContextType => {\n  const context = useContext(NotificationContext);\n  if (context === undefined) {\n    throw new Error(\n      \"useNotifications must be used within a NotificationProvider\"\n    );\n  }\n  return context;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IACVC,aAAa,EACbC,UAAU,EACVC,SAAS,EACTC,QAAQ,QAEH,OAAO;AACd,SAASC,EAAE,QAAQ,kBAAkB;AACrC,SAASC,YAAY,QAAQ,MAAM;AACnC,OAAOC,iBAAiB,MAAM,uCAAuC;;AAErE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAgCA,MAAMC,mBAAmB,gBAAGT,aAAa,CACvCU,SACF,CAAC;AAMD,OAAO,MAAMC,oBAAyD,GAAGA,CAAC;EACxEC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGZ,QAAQ,CAAqB,EAAE,CAAC;EAC1E,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAErDD,SAAS,CAAC,MAAM;IACd;IACA,MAAMgB,SAAS,GAAGd,EAAE,CAAC,0BAA0B,EAAE;MAC/Ce,UAAU,EAAE,CAAC,WAAW,CAAC;MACzBC,WAAW,EAAE;IACf,CAAC,CAAC;IAEFF,SAAS,CAACG,EAAE,CAAC,SAAS,EAAE,MAAM;MAC5BC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClDN,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,CAAC;IAEFC,SAAS,CAACG,EAAE,CAAC,YAAY,EAAE,MAAM;MAC/BC,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtDN,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,CAAC;;IAEF;IACAC,SAAS,CAACG,EAAE,CAAC,qBAAqB,EAAGG,IAAsB,IAAK;MAC9DF,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEC,IAAI,CAAC;;MAE3D;MACAT,gBAAgB,CAAEU,IAAI,IAAK,CAACD,IAAI,EAAE,GAAGC,IAAI,CAAC,CAAC;;MAE3C;MACApB,YAAY,CAACqB,OAAO,CAAC;QACnBC,OAAO,EAAEH,IAAI,CAACI,KAAK;QACnBC,WAAW,EAAEL,IAAI,CAACG,OAAO;QACzBG,QAAQ,EAAE,CAAC;QACXC,SAAS,EAAE,UAAU;QACrBC,OAAO,EAAEA,CAAA,KAAM;UACb;UACAV,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,IAAI,CAAC;QAC5C;MACF,CAAC,CAAC;;MAEF;MACA,IAAI;QACF,MAAMS,KAAK,GAAG,IAAIC,KAAK,CAAC5B,iBAAiB,CAAC;QAC1C2B,KAAK,CACFE,IAAI,CAAC,CAAC,CACNC,KAAK,CAAC,MAAMd,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC,CAAC;MAClE,CAAC,CAAC,MAAM;QACN;MAAA;IAEJ,CAAC,CAAC;;IAEF;IACA,OAAO,MAAM;MACXL,SAAS,CAACmB,KAAK,CAAC,CAAC;IACnB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,UAAU,GAAIC,EAAU,IAAK;IACjCxB,gBAAgB,CAAEU,IAAI,IACpBA,IAAI,CAACe,GAAG,CAAEC,KAAK,IAAMA,KAAK,CAACF,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGE,KAAK;MAAEC,IAAI,EAAE;IAAK,CAAC,GAAGD,KAAM,CAC1E,CAAC;EACH,CAAC;EAED,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1B5B,gBAAgB,CAAEU,IAAI,IAAKA,IAAI,CAACe,GAAG,CAAEC,KAAK,KAAM;MAAE,GAAGA,KAAK;MAAEC,IAAI,EAAE;IAAK,CAAC,CAAC,CAAC,CAAC;EAC7E,CAAC;EAED,MAAME,iBAAiB,GAAIL,EAAU,IAAK;IACxCxB,gBAAgB,CAAEU,IAAI,IAAKA,IAAI,CAACoB,MAAM,CAAEJ,KAAK,IAAKA,KAAK,CAACF,EAAE,KAAKA,EAAE,CAAC,CAAC;EACrE,CAAC;EAED,MAAMO,qBAAqB,GAAGA,CAAA,KAAM;IAClC/B,gBAAgB,CAAC,EAAE,CAAC;EACtB,CAAC;EAED,MAAMgC,WAAW,GAAGjC,aAAa,CAAC+B,MAAM,CAAEJ,KAAK,IAAK,CAACA,KAAK,CAACC,IAAI,CAAC,CAACM,MAAM;EAEvE,MAAMC,KAA8B,GAAG;IACrCnC,aAAa;IACbiC,WAAW;IACXT,UAAU;IACVK,aAAa;IACbC,iBAAiB;IACjBE,qBAAqB;IACrB9B;EACF,CAAC;EAED,oBACER,OAAA,CAACC,mBAAmB,CAACyC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAArC,QAAA,EACxCA;EAAQ;IAAAuC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACmB,CAAC;AAEnC,CAAC;AAACzC,EAAA,CA9FWF,oBAAyD;AAAA4C,EAAA,GAAzD5C,oBAAyD;AAgGtE,OAAO,MAAM6C,gBAAgB,GAAGA,CAAA,KAA+B;EAAAC,GAAA;EAC7D,MAAMC,OAAO,GAAGzD,UAAU,CAACQ,mBAAmB,CAAC;EAC/C,IAAIiD,OAAO,KAAKhD,SAAS,EAAE;IACzB,MAAM,IAAIiD,KAAK,CACb,6DACF,CAAC;EACH;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CARWD,gBAAgB;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}