# ninja log v5
7	64	0	C:/Users/<USER>/Desktop/sg-alumni/alumni-mobile-app/node_modules/react-native-reanimated/android/.cxx/Debug/46416659/x86_64/CMakeFiles/cmake.verify_globs	50a382c1ddd2294a
3	4636	7750367601960133	src/main/cpp/worklets/CMakeFiles/worklets.dir/baa02494a34db5a33303ebf2c9592326/Common/cpp/worklets/Tools/JSLogger.cpp.o	58f623aa165a845f
268	5344	7750367607681338	src/main/cpp/worklets/CMakeFiles/worklets.dir/f96306655a2392a68b7cb0310303e3e7/cpp/worklets/Tools/JSISerializer.cpp.o	7bf41b0ddacb36c
496	6015	7750367611267509	src/main/cpp/worklets/CMakeFiles/worklets.dir/f96306655a2392a68b7cb0310303e3e7/cpp/worklets/Tools/JSScheduler.cpp.o	859a692913b78fa2
796	6731	7750367631457914	src/main/cpp/worklets/CMakeFiles/worklets.dir/7af6c96b14aea9d29e7758a49d1fa273/NativeModules/WorkletsModuleProxy.cpp.o	145bf8c56dbfbeca
1183	7341	7750367633640090	src/main/cpp/worklets/CMakeFiles/worklets.dir/7af6c96b14aea9d29e7758a49d1fa273/Registries/EventHandlerRegistry.cpp.o	4b3b07009d1cd0eb
1616	7927	7750367631707939	src/main/cpp/worklets/CMakeFiles/worklets.dir/37c5213f716e4001611922c8986d108c/WorkletsModuleProxySpec.cpp.o	af4b7b744060759d
2232	8402	7750367648918780	src/main/cpp/worklets/CMakeFiles/worklets.dir/31ffcf47bca7cd3078890f62099c8b49/worklets/SharedItems/Shareables.cpp.o	5a06a17df571f61c
3034	9001	7750367635143947	src/main/cpp/worklets/CMakeFiles/worklets.dir/7af6c96b14aea9d29e7758a49d1fa273/Registries/WorkletRuntimeRegistry.cpp.o	dd83e81822314349
3647	9616	7750367645397256	src/main/cpp/worklets/CMakeFiles/worklets.dir/f96306655a2392a68b7cb0310303e3e7/cpp/worklets/Tools/AsyncQueue.cpp.o	5313f6da227bfacc
4127	10389	7750367651128442	src/main/cpp/worklets/CMakeFiles/worklets.dir/31ffcf47bca7cd3078890f62099c8b49/worklets/Tools/ReanimatedJSIUtils.cpp.o	2fbc9d5af1658288
4654	11326	7750367685760948	src/main/cpp/worklets/CMakeFiles/worklets.dir/31ffcf47bca7cd3078890f62099c8b49/worklets/Tools/ReanimatedVersion.cpp.o	1a6356d672818e4c
5345	12010	7750367674538072	src/main/cpp/worklets/CMakeFiles/worklets.dir/904e03e0566c29472b31fa74f311e2a9/RNRuntimeWorkletDecorator.cpp.o	7ff6b19b052913ff
6024	12423	7750367670094385	src/main/cpp/worklets/CMakeFiles/worklets.dir/f96306655a2392a68b7cb0310303e3e7/cpp/worklets/Tools/UIScheduler.cpp.o	7feec2ffb0ef1d3c
6733	12991	7750367680470948	src/main/cpp/worklets/CMakeFiles/worklets.dir/31ffcf47bca7cd3078890f62099c8b49/worklets/Tools/WorkletEventHandler.cpp.o	7a007b8738131888
7342	13652	7750367694730191	src/main/cpp/worklets/CMakeFiles/worklets.dir/904e03e0566c29472b31fa74f311e2a9/ReanimatedHermesRuntime.cpp.o	9859eae3b41b9c2
7928	14645	7750367692747633	src/main/cpp/worklets/CMakeFiles/worklets.dir/7af6c96b14aea9d29e7758a49d1fa273/WorkletRuntime/ReanimatedRuntime.cpp.o	10816c41a20394d3
8403	15299	7750367714349674	src/main/cpp/worklets/CMakeFiles/worklets.dir/7af6c96b14aea9d29e7758a49d1fa273/WorkletRuntime/WorkletRuntime.cpp.o	3ea488c2b6b59597
10390	15879	7750367709056841	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/PlatformLogger.cpp.o	269b10280aa43f85
12992	16343	7750367714019659	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/cafd7f2cc3a3d5689f72655791390dd5/Fabric/PropsRegistry.cpp.o	639ff21741db687f
13654	16791	7750367723937291	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/cafd7f2cc3a3d5689f72655791390dd5/Fabric/ReanimatedCommitHook.cpp.o	19b00819a8c8fa12
14646	17239	7750367730566503	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/14dbe95b6c3b02a5b0515666739d2a4d/LayoutAnimationsUtils.cpp.o	da8daf76cb22b2ec
15300	17591	7750367736170565	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/cafd7f2cc3a3d5689f72655791390dd5/Fabric/ShadowTreeCloner.cpp.o	faebdc241d617285
15880	17946	7750367740777404	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/14dbe95b6c3b02a5b0515666739d2a4d/LayoutAnimationsProxy.cpp.o	5188957c0dba6cee
16344	18728	7750367745977312	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/cafd7f2cc3a3d5689f72655791390dd5/Fabric/ReanimatedMountHook.cpp.o	f319615b8ced9745
9002	19316	7750367741047381	src/main/cpp/worklets/CMakeFiles/worklets.dir/904e03e0566c29472b31fa74f311e2a9/WorkletRuntimeDecorator.cpp.o	10b90e0b86884543
9617	19895	7750367745497328	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/AndroidUIScheduler.cpp.o	55a08761a7abad3b
12424	20512	7750367746107339	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/b83d658e1dcd98633c6ea0382c7a8424/AnimatedSensorModule.cpp.o	56ab2203bde38416
12011	21029	7750367765752035	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsOnLoad.cpp.o	c1ac8c12d462ead3
11328	21738	7750367767469130	src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsModule.cpp.o	15f34f38d7e52f99
16792	22866	7750367793955969	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/14dbe95b6c3b02a5b0515666739d2a4d/LayoutAnimationsManager.cpp.o	6040872f32d92a66
17592	23472	7750367803273172	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/2c92e9484f829ce332f57444920e1bf5/RNRuntimeDecorator.cpp.o	208a69247e22710
19899	23477	7750367805143168	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/cafd7f2cc3a3d5689f72655791390dd5/Tools/FeaturesConfig.cpp.o	b5f9b1a8caf0ed2
18729	23653	7750367812097184	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/820de9f96c4f5fc4c28878888c15441a/ReanimatedModuleProxySpec.cpp.o	86075a179c31e3f3
17948	25614	7750367831531739	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/JNIHelper.cpp.o	98535861d4c25dbd
17240	26391	7750367839278934	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/820de9f96c4f5fc4c28878888c15441a/ReanimatedModuleProxy.cpp.o	6f38ca48b9396f0a
21739	26414	7750367839648938	../../../../build/intermediates/cxx/Debug/46416659/obj/x86_64/libworklets.so	ed9034e5c3fdc54
19317	26483	7750367840660830	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/2c92e9484f829ce332f57444920e1bf5/UIRuntimeDecorator.cpp.o	38d30f4e8a054706
21031	28628	7750367862222329	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/LayoutAnimations.cpp.o	a162b0eccae07bcd
22867	31592	7750367891922604	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/OnLoad.cpp.o	94096a9a307d0108
20514	34029	7750367915947825	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/NativeProxy.cpp.o	ea0413d0e4c9d724
34030	35547	7750367931397330	../../../../build/intermediates/cxx/Debug/46416659/obj/x86_64/libreanimated.so	f715e0d854afd5c3
7	75	0	C:/Users/<USER>/Desktop/sg-alumni/alumni-mobile-app/node_modules/react-native-reanimated/android/.cxx/Debug/46416659/x86_64/CMakeFiles/cmake.verify_globs	50a382c1ddd2294a
11	109	0	C:/Users/<USER>/Desktop/sg-alumni/alumni-mobile-app/node_modules/react-native-reanimated/android/.cxx/Debug/46416659/x86_64/CMakeFiles/cmake.verify_globs	50a382c1ddd2294a
9	187	0	C:/Users/<USER>/Desktop/sg-alumni/alumni-mobile-app/node_modules/react-native-reanimated/android/.cxx/Debug/46416659/x86_64/CMakeFiles/cmake.verify_globs	50a382c1ddd2294a
8	86	0	C:/Users/<USER>/Desktop/sg-alumni/alumni-mobile-app/node_modules/react-native-reanimated/android/.cxx/Debug/46416659/x86_64/CMakeFiles/cmake.verify_globs	50a382c1ddd2294a
