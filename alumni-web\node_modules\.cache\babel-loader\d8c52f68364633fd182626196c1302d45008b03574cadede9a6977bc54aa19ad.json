{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\sg-alumni\\\\alumni-web\\\\src\\\\Pages\\\\GroupManagement\\\\GroupDetails.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Card, Table, Button, Space, Typography, Input, Modal, Select, message, Popconfirm, Tag, Avatar, Row, Col, Statistic, Tabs, DatePicker, Tooltip, Dropdown, Menu } from \"antd\";\nimport { ArrowLeftOutlined, UserAddOutlined, UserOutlined, UserDeleteOutlined, MessageOutlined, DeleteOutlined, SearchOutlined, TeamOutlined, CalendarOutlined, StopOutlined, CheckCircleOutlined, InfoCircleOutlined, UnlockOutlined } from \"@ant-design/icons\";\nimport { useParams, useNavigate } from \"react-router-dom\";\nimport { apiService } from \"../../services/apiService\";\nimport { io } from \"socket.io-client\";\nimport \"./GroupManagement.css\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Search\n} = Input;\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\nconst {\n  TabPane\n} = Tabs;\nconst GroupDetails = () => {\n  _s();\n  const {\n    groupId\n  } = useParams();\n  const navigate = useNavigate();\n  const [group, setGroup] = useState(null);\n  const [members, setMembers] = useState([]);\n  const [messages, setMessages] = useState([]);\n  const [allUsers, setAllUsers] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [searchText, setSearchText] = useState(\"\");\n  const [isAddMemberModalVisible, setIsAddMemberModalVisible] = useState(false);\n  const [selectedUsers, setSelectedUsers] = useState([]);\n  const [activeTab, setActiveTab] = useState(\"members\");\n  const [isExternalUserModalVisible, setIsExternalUserModalVisible] = useState(false);\n  const [externalUsers, setExternalUsers] = useState([]);\n  const [selectedExternalUsers, setSelectedExternalUsers] = useState([]);\n\n  // Message deletion states\n  const [deleteModalVisible, setDeleteModalVisible] = useState(false);\n  const [deleteType, setDeleteType] = useState(\"single\");\n  const [selectedMessageId, setSelectedMessageId] = useState(\"\");\n  const [selectedUserId, setSelectedUserId] = useState(\"\");\n  const [dateRange, setDateRange] = useState(null);\n  const [deletingMessages, setDeletingMessages] = useState(false);\n  const [socket, setSocket] = useState(null);\n  useEffect(() => {\n    if (groupId) {\n      fetchGroupDetails();\n      fetchGroupMembers();\n      fetchGroupMessages();\n      fetchAvailableUsers();\n    }\n  }, [groupId]);\n\n  // Socket connection for real-time message deletion updates\n  useEffect(() => {\n    if (!groupId) return;\n    const newSocket = io(\"http://192.168.1.49:3008\", {\n      transports: [\"websocket\"],\n      autoConnect: true\n    });\n    newSocket.on(\"connect\", () => {\n      console.log(\"🔗 Connected to message server\");\n      newSocket.emit(\"joinGroup\", groupId);\n    });\n\n    // Listen for single message deletion\n    newSocket.on(\"messageDeleted\", deletionData => {\n      console.log(\"📧 Message deleted:\", deletionData);\n      setMessages(prevMessages => prevMessages.map(msg => msg.id === deletionData.id.toString() ? {\n        ...msg,\n        isDeleted: true,\n        deletedBy: deletionData.deletedBy,\n        deletedAt: deletionData.deletedAt\n      } : msg));\n    });\n\n    // Listen for bulk message deletion\n    newSocket.on(\"messagesBulkDeleted\", bulkDeletionData => {\n      console.log(\"📧 Bulk messages deleted:\", bulkDeletionData);\n      if (bulkDeletionData.type === \"bulk_delete_all\") {\n        // Mark all messages as deleted\n        setMessages(prevMessages => prevMessages.map(msg => ({\n          ...msg,\n          isDeleted: true,\n          deletedBy: bulkDeletionData.deletedBy,\n          deletedAt: bulkDeletionData.deletedAt\n        })));\n      } else {\n        // Mark specific messages as deleted\n        setMessages(prevMessages => prevMessages.map(msg => bulkDeletionData.deletedMessageIds.includes(parseInt(msg.id)) ? {\n          ...msg,\n          isDeleted: true,\n          deletedBy: bulkDeletionData.deletedBy,\n          deletedAt: bulkDeletionData.deletedAt\n        } : msg));\n      }\n    });\n    setSocket(newSocket);\n    return () => {\n      newSocket.disconnect();\n    };\n  }, [groupId]);\n  const fetchGroupDetails = async () => {\n    try {\n      const response = await apiService.getGroupById(groupId);\n      if (response.data.success) {\n        setGroup(response.data.group);\n      }\n    } catch (error) {\n      console.error(\"Error fetching group details:\", error);\n      message.error(\"Failed to fetch group details\");\n    }\n  };\n  const fetchGroupMembers = async () => {\n    setLoading(true);\n    try {\n      const response = await apiService.getGroupMembers(groupId);\n      if (response.data.success) {\n        setMembers(response.data.members || []);\n      }\n    } catch (error) {\n      console.error(\"Error fetching group members:\", error);\n      message.error(\"Failed to fetch group members\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchGroupMessages = async () => {\n    setLoading(true);\n    try {\n      const response = await apiService.getGroupMessages(groupId);\n      console.log(response);\n      if (response) {\n        setMessages(response.data || []);\n      }\n    } catch (error) {\n      console.error(\"Error fetching group messages:\", error);\n      message.error(\"Failed to fetch group messages\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchAvailableUsers = async () => {\n    if (!groupId) return;\n    try {\n      console.log(\"🔍 Fetching available users for group:\", groupId);\n      const response = await apiService.getAvailableUsersForGroup(groupId);\n      if (response.data.success) {\n        console.log(\"👥 Available users:\", response.data.users);\n        console.log(\"📋 Group info:\", response.data.groupInfo);\n        setAllUsers(response.data.users || []);\n      }\n    } catch (error) {\n      console.error(\"Error fetching available users:\", error);\n      message.error(\"Failed to fetch available users\");\n    }\n  };\n  const handleAddMembers = async () => {\n    try {\n      const promises = selectedUsers.map(userId => apiService.addGroupMember({\n        userId,\n        groupId: groupId\n      }));\n      await Promise.all(promises);\n      message.success(`${selectedUsers.length} member(s) added successfully`);\n      setIsAddMemberModalVisible(false);\n      setSelectedUsers([]);\n      fetchGroupMembers();\n    } catch (error) {\n      console.error(\"Error adding members:\", error);\n      message.error(\"Failed to add members\");\n    }\n  };\n  const handleRemoveMember = async userId => {\n    try {\n      await apiService.removeGroupMember(userId, groupId);\n      message.success(\"Member removed successfully\");\n      fetchGroupMembers();\n    } catch (error) {\n      console.error(\"Error removing member:\", error);\n      message.error(\"Failed to remove member\");\n    }\n  };\n  const handleAddExternalUser = async () => {\n    if (!groupId) return;\n    try {\n      // Fetch all users for external selection\n      const response = await apiService.getAllUsers();\n      if (response.data.success) {\n        var _group$batches;\n        const currentMemberIds = members.map(m => m.id);\n        const groupBatchIds = (group === null || group === void 0 ? void 0 : (_group$batches = group.batches) === null || _group$batches === void 0 ? void 0 : _group$batches.map(b => b.id)) || [];\n\n        // Filter to show only external users (not in group's batches and not current members)\n        const availableExternalUsers = response.data.users.filter(user => {\n          // Exclude current members\n          if (currentMemberIds.includes(user.id)) return false;\n\n          // If group has specific batches, only show users NOT from those batches\n          if (groupBatchIds.length > 0) {\n            var _user$trainee;\n            return !((_user$trainee = user.trainee) !== null && _user$trainee !== void 0 && _user$trainee.batchId) || !groupBatchIds.includes(user.trainee.batchId);\n          }\n\n          // If group is universal, show all non-members\n          return true;\n        });\n        setExternalUsers(availableExternalUsers);\n        setSelectedExternalUsers([]);\n        setIsExternalUserModalVisible(true);\n      }\n    } catch (error) {\n      console.error(\"Error fetching external users:\", error);\n      message.error(\"Failed to fetch external users\");\n    }\n  };\n  const handleSaveExternalUsers = async () => {\n    if (!groupId || selectedExternalUsers.length === 0) return;\n    try {\n      const promises = selectedExternalUsers.map(userId => apiService.addGroupMember({\n        userId,\n        groupId: groupId\n      }));\n      await Promise.all(promises);\n      message.success(`${selectedExternalUsers.length} external user(s) added successfully`);\n      setIsExternalUserModalVisible(false);\n      setSelectedExternalUsers([]);\n      fetchGroupMembers();\n    } catch (error) {\n      console.error(\"Error adding external users:\", error);\n      message.error(\"Failed to add external users\");\n    }\n  };\n  const handleBlockMember = async userId => {\n    try {\n      await apiService.blockGroupMember(userId, groupId);\n      message.success(\"Member blocked from messaging successfully\");\n      fetchGroupMembers();\n    } catch (error) {\n      console.error(\"Error blocking member:\", error);\n      message.error(\"Failed to block member\");\n    }\n  };\n  const handleUnblockMember = async userId => {\n    try {\n      await apiService.unblockGroupMember(userId, groupId);\n      message.success(\"Member unblocked successfully\");\n      fetchGroupMembers();\n    } catch (error) {\n      console.error(\"Error unblocking member:\", error);\n      message.error(\"Failed to unblock member\");\n    }\n  };\n  const handleDeleteMessage = async messageId => {\n    try {\n      await apiService.deleteMessage(messageId);\n      message.success(\"Message deleted successfully\");\n      fetchGroupMessages();\n    } catch (error) {\n      console.error(\"Error deleting message:\", error);\n      message.error(\"Failed to delete message\");\n    }\n  };\n  const handleDeleteAllMessages = async () => {\n    try {\n      await apiService.deleteAllGroupMessages(groupId);\n      message.success(\"All messages deleted successfully\");\n      fetchGroupMessages();\n    } catch (error) {\n      console.error(\"Error deleting all messages:\", error);\n      message.error(\"Failed to delete all messages\");\n    }\n  };\n  const handleDeleteMessagesByDateRange = async (startDate, endDate) => {\n    try {\n      await apiService.deleteGroupMessagesByDateRange(groupId, startDate, endDate);\n      message.success(\"Messages deleted successfully\");\n      fetchGroupMessages();\n    } catch (error) {\n      console.error(\"Error deleting messages by date range:\", error);\n      message.error(\"Failed to delete messages\");\n    }\n  };\n\n  // New functions for enhanced message deletion\n  const handleDeleteUserMessages = async userId => {\n    try {\n      setDeletingMessages(true);\n      await apiService.deleteAllUserMessages(userId);\n      message.success(\"All user messages deleted successfully\");\n      fetchGroupMessages();\n    } catch (error) {\n      console.error(\"Error deleting user messages:\", error);\n      message.error(\"Failed to delete user messages\");\n    } finally {\n      setDeletingMessages(false);\n    }\n  };\n  const handleDeleteUserMessagesByDateRange = async (userId, startDate, endDate) => {\n    try {\n      setDeletingMessages(true);\n      await apiService.deleteUserMessagesByDateRange(userId, startDate, endDate);\n      message.success(\"User messages in date range deleted successfully\");\n      fetchGroupMessages();\n    } catch (error) {\n      console.error(\"Error deleting user messages by date range:\", error);\n      message.error(\"Failed to delete user messages\");\n    } finally {\n      setDeletingMessages(false);\n    }\n  };\n  const openDeleteModal = (type, messageId, userId) => {\n    setDeleteType(type);\n    setSelectedMessageId(messageId || \"\");\n    setSelectedUserId(userId || \"\");\n    setDeleteModalVisible(true);\n  };\n  const handleConfirmDelete = async () => {\n    if (deleteType === \"single\" && selectedMessageId) {\n      await handleDeleteMessage(selectedMessageId);\n    } else if (deleteType === \"all\") {\n      await handleDeleteAllMessages();\n    } else if (deleteType === \"user\" && selectedUserId) {\n      await handleDeleteUserMessages(selectedUserId);\n    } else if (deleteType === \"dateRange\" && dateRange) {\n      const startDate = dateRange[0].format(\"YYYY-MM-DD\");\n      const endDate = dateRange[1].format(\"YYYY-MM-DD\");\n      if (selectedUserId) {\n        await handleDeleteUserMessagesByDateRange(selectedUserId, startDate, endDate);\n      } else {\n        await handleDeleteMessagesByDateRange(startDate, endDate);\n      }\n    }\n    setDeleteModalVisible(false);\n    setDateRange(null);\n    setSelectedUserId(\"\");\n    setSelectedMessageId(\"\");\n  };\n  const availableUsers = allUsers.filter(user => {\n    var _user$username;\n    return !members.some(member => member.userId === user.id) && ((_user$username = user.username) === null || _user$username === void 0 ? void 0 : _user$username.toLowerCase().includes(searchText.toLowerCase()));\n  });\n  const filteredMessages = messages.filter(message => {\n    var _message$content, _message$user, _message$user$usernam;\n    return ((_message$content = message.content) === null || _message$content === void 0 ? void 0 : _message$content.toLowerCase().includes(searchText.toLowerCase())) || ((_message$user = message.user) === null || _message$user === void 0 ? void 0 : (_message$user$usernam = _message$user.username) === null || _message$user$usernam === void 0 ? void 0 : _message$user$usernam.toLowerCase().includes(searchText.toLowerCase()));\n  });\n  const memberColumns = [{\n    title: \"Member\",\n    dataIndex: \"user\",\n    key: \"user\",\n    render: user => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n        src: user === null || user === void 0 ? void 0 : user.image,\n        icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 43\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 517,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          children: (user === null || user === void 0 ? void 0 : user.username) || \"Unknown User\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 520,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: (user === null || user === void 0 ? void 0 : user.email) || \"No email\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 516,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: \"Joined\",\n    dataIndex: \"joinedAt\",\n    key: \"joinedAt\",\n    render: date => new Date(date).toLocaleDateString()\n  }, {\n    title: \"Status\",\n    key: \"status\",\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [record.isBlocked ? /*#__PURE__*/_jsxDEV(Tag, {\n        color: \"red\",\n        icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 36\n        }, this),\n        children: \"Blocked\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 538,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Tag, {\n        color: \"green\",\n        icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 38\n        }, this),\n        children: \"Active\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 542,\n        columnNumber: 13\n      }, this), record.blockedAt && /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: `Blocked on ${new Date(record.blockedAt).toLocaleString()}`,\n        children: /*#__PURE__*/_jsxDEV(InfoCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 547,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 536,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: \"Actions\",\n    key: \"actions\",\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [record.isBlocked ? /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"Are you sure you want to unblock this member? They will be able to send messages again.\",\n        onConfirm: () => handleUnblockMember(record.userId),\n        okText: \"Yes\",\n        cancelText: \"No\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(UnlockOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 44\n          }, this),\n          size: \"small\",\n          children: \"Unblock\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 564,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"Are you sure you want to block this member? They will not be able to send messages in this group.\",\n        onConfirm: () => handleBlockMember(record.userId),\n        okText: \"Yes\",\n        cancelText: \"No\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 29\n          }, this),\n          size: \"small\",\n          children: \"Block\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 575,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"Are you sure you want to remove this member from the group? This action cannot be undone.\",\n        onConfirm: () => handleRemoveMember(record.userId),\n        okText: \"Yes\",\n        cancelText: \"No\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(UserDeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 34\n          }, this),\n          size: \"small\",\n          children: \"Remove\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 586,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 9\n    }, this)\n  }];\n  const messageColumns = [{\n    title: \"Message\",\n    dataIndex: \"content\",\n    key: \"content\",\n    render: (content, record) => {\n      var _record$user, _record$user2, _record$deletedBy;\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: (_record$user = record.user) === null || _record$user === void 0 ? void 0 : _record$user.image,\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: ((_record$user2 = record.user) === null || _record$user2 === void 0 ? void 0 : _record$user2.username) || \"Unknown User\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            style: {\n              fontSize: \"12px\"\n            },\n            children: new Date(record.createdAt).toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 13\n          }, this), record.isDeleted && /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"red\",\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 38\n            }, this),\n            children: \"Deleted\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 8\n          },\n          children: record.isDeleted ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: \"8px 12px\",\n              backgroundColor: \"#fff2f0\",\n              border: \"1px solid #ffccc7\",\n              borderRadius: \"6px\",\n              fontStyle: \"italic\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              style: {\n                color: \"#cf1322\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(DeleteOutlined, {\n                style: {\n                  marginRight: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 19\n              }, this), (_record$deletedBy = record.deletedBy) !== null && _record$deletedBy !== void 0 && _record$deletedBy.isAdmin ? \"This message was deleted by admin\" : \"This message was deleted by user\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 631,\n              columnNumber: 17\n            }, this), record.deletedAt && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 4\n              },\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                style: {\n                  fontSize: \"11px\",\n                  color: \"#8c8c8c\"\n                },\n                children: [\"Deleted on \", new Date(record.deletedAt).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: content || \"No content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 650,\n              columnNumber: 17\n            }, this), record.images && record.images.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8\n              },\n              children: record.images.map((img, index) => /*#__PURE__*/_jsxDEV(\"img\", {\n                src: img.imageUrl,\n                alt: \"Message attachment\",\n                style: {\n                  maxWidth: 100,\n                  maxHeight: 100,\n                  marginRight: 8\n                }\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 654,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 652,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 620,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 607,\n        columnNumber: 9\n      }, this);\n    }\n  }, {\n    title: \"Actions\",\n    key: \"actions\",\n    render: (text, record) => {\n      var _record$user3;\n      // Don't show actions for deleted messages\n      if (record.isDeleted) {\n        return /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          style: {\n            fontStyle: \"italic\"\n          },\n          children: \"No actions available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 680,\n          columnNumber: 13\n        }, this);\n      }\n      const deleteMenu = /*#__PURE__*/_jsxDEV(Menu, {\n        children: [/*#__PURE__*/_jsxDEV(Menu.Item, {\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 690,\n            columnNumber: 21\n          }, this),\n          onClick: () => openDeleteModal(\"single\", record.id),\n          children: \"Delete This Message\"\n        }, \"single\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 688,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Menu.Item, {\n          icon: /*#__PURE__*/_jsxDEV(UserDeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 697,\n            columnNumber: 21\n          }, this),\n          onClick: () => openDeleteModal(\"user\", undefined, record.user.id),\n          children: [\"Delete All Messages by \", ((_record$user3 = record.user) === null || _record$user3 === void 0 ? void 0 : _record$user3.username) || \"Unknown User\"]\n        }, \"user\", true, {\n          fileName: _jsxFileName,\n          lineNumber: 695,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Menu.Divider, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 702,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Menu.Item, {\n          icon: /*#__PURE__*/_jsxDEV(CalendarOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 705,\n            columnNumber: 21\n          }, this),\n          onClick: () => openDeleteModal(\"dateRange\", undefined, record.user.id),\n          children: \"Delete User Messages by Date Range\"\n        }, \"userDateRange\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 703,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 687,\n        columnNumber: 11\n      }, this);\n      return /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Popconfirm, {\n          title: \"Are you sure you want to delete this message? This action cannot be undone.\",\n          onConfirm: () => handleDeleteMessage(record.id),\n          okText: \"Yes\",\n          cancelText: \"No\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 36\n            }, this),\n            size: \"small\",\n            children: \"Delete\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 723,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 717,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n          menu: {\n            items: deleteMenu.props.children\n          },\n          trigger: [\"click\"],\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            size: \"small\",\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 42\n            }, this),\n            children: \"More Options\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 727,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 716,\n        columnNumber: 11\n      }, this);\n    }\n  }];\n  if (!group) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 742,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"group-management-container\",\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ArrowLeftOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 23\n              }, this),\n              onClick: () => navigate(\"/group-management\"),\n              children: \"Back to Group Forums\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 752,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Avatar, {\n              src: group.image,\n              icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 47\n              }, this),\n              size: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 758,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Title, {\n                level: 3,\n                style: {\n                  margin: 0\n                },\n                children: group.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 760,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"Group Forum Management\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 763,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 751,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 750,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 749,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 748,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"Total Members\",\n            value: members.length,\n            prefix: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 777,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 774,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 773,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 772,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"Total Messages\",\n            value: messages.length,\n            prefix: /*#__PURE__*/_jsxDEV(MessageOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 786,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 783,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 782,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 781,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"Created\",\n            value: new Date(group.createdAt).toLocaleDateString(),\n            prefix: /*#__PURE__*/_jsxDEV(CalendarOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 795,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 791,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 790,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 771,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        activeKey: activeTab,\n        onChange: setActiveTab,\n        children: [/*#__PURE__*/_jsxDEV(TabPane, {\n          tab: `Members (${members.length})`,\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            style: {\n              marginBottom: 16\n            },\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 6,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"Total Members\",\n                  value: members.length,\n                  prefix: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 812,\n                    columnNumber: 29\n                  }, this),\n                  valueStyle: {\n                    color: \"#1890ff\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 809,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 808,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 6,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"Available Users\",\n                  value: availableUsers.length,\n                  prefix: /*#__PURE__*/_jsxDEV(UserAddOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 822,\n                    columnNumber: 29\n                  }, this),\n                  valueStyle: {\n                    color: \"#52c41a\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 819,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 818,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 817,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 6,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"Admin Members\",\n                  value: members.filter(member => {\n                    var _member$user;\n                    return ((_member$user = member.user) === null || _member$user === void 0 ? void 0 : _member$user.role) === \"ADMIN\";\n                  }).length,\n                  prefix: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 835,\n                    columnNumber: 29\n                  }, this),\n                  valueStyle: {\n                    color: \"#f5222d\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 829,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 827,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 6,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"HR Members\",\n                  value: members.filter(member => {\n                    var _member$user2;\n                    return ((_member$user2 = member.user) === null || _member$user2 === void 0 ? void 0 : _member$user2.role) === \"HR\";\n                  }).length,\n                  prefix: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 848,\n                    columnNumber: 29\n                  }, this),\n                  valueStyle: {\n                    color: \"#fa8c16\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 842,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 841,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 840,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 806,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            style: {\n              marginBottom: 16\n            },\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"Blocked Members\",\n                  value: members.filter(member => member.isBlocked).length,\n                  prefix: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 862,\n                    columnNumber: 29\n                  }, this),\n                  valueStyle: {\n                    color: \"#f5222d\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 859,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 858,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"Active Members\",\n                  value: members.filter(member => !member.isBlocked).length,\n                  prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 872,\n                    columnNumber: 29\n                  }, this),\n                  valueStyle: {\n                    color: \"#52c41a\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 869,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 868,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 867,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(Statistic, {\n                  title: \"Block Rate\",\n                  value: members.length > 0 ? Math.round(members.filter(member => member.isBlocked).length / members.length * 100) : 0,\n                  suffix: \"%\",\n                  prefix: /*#__PURE__*/_jsxDEV(InfoCircleOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 892,\n                    columnNumber: 29\n                  }, this),\n                  valueStyle: {\n                    color: \"#722ed1\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 879,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 878,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 877,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 856,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: 16\n            },\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              justify: \"space-between\",\n              align: \"middle\",\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                children: /*#__PURE__*/_jsxDEV(Search, {\n                  placeholder: \"Search members...\",\n                  allowClear: true,\n                  style: {\n                    width: 300\n                  },\n                  prefix: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 906,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 902,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 901,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                children: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    children: [\"Showing \", members.length, \" of\", \" \", members.length + availableUsers.length, \" total users\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 911,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"primary\",\n                    icon: /*#__PURE__*/_jsxDEV(UserAddOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 917,\n                      columnNumber: 29\n                    }, this),\n                    onClick: () => setIsAddMemberModalVisible(true),\n                    children: \"Add Members\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 915,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"default\",\n                    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 924,\n                      columnNumber: 29\n                    }, this),\n                    onClick: handleAddExternalUser,\n                    children: \"Add External User\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 922,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 910,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 909,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 900,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 899,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Table, {\n            columns: memberColumns,\n            dataSource: members,\n            rowKey: \"id\",\n            loading: loading,\n            pagination: {\n              pageSize: 10\n            },\n            scroll: {\n              x: \"max-content\"\n            },\n            size: \"middle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 934,\n            columnNumber: 13\n          }, this)]\n        }, \"members\", true, {\n          fileName: _jsxFileName,\n          lineNumber: 804,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: `Messages (${messages.length})`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: 16\n            },\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              justify: \"space-between\",\n              align: \"middle\",\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                children: /*#__PURE__*/_jsxDEV(Search, {\n                  placeholder: \"Search messages...\",\n                  allowClear: true,\n                  value: searchText,\n                  onChange: e => setSearchText(e.target.value),\n                  style: {\n                    width: 300\n                  },\n                  prefix: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 955,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 949,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 948,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                children: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(Popconfirm, {\n                    title: \"Are you sure you want to delete all messages? This will permanently delete all messages in this group and cannot be undone.\",\n                    onConfirm: handleDeleteAllMessages,\n                    okText: \"Yes\",\n                    cancelText: \"No\",\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      danger: true,\n                      icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 966,\n                        columnNumber: 44\n                      }, this),\n                      children: \"Delete All Messages\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 966,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 960,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    icon: /*#__PURE__*/_jsxDEV(CalendarOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 972,\n                      columnNumber: 29\n                    }, this),\n                    onClick: () => openDeleteModal(\"dateRange\"),\n                    children: \"Delete by Date Range\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 971,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 959,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 958,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 947,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 946,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Table, {\n            columns: messageColumns,\n            dataSource: filteredMessages,\n            rowKey: \"id\",\n            loading: loading,\n            pagination: {\n              pageSize: 10\n            },\n            scroll: {\n              x: \"max-content\"\n            },\n            size: \"middle\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 982,\n            columnNumber: 13\n          }, this)]\n        }, \"messages\", true, {\n          fileName: _jsxFileName,\n          lineNumber: 945,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 803,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 802,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"Add Members to Group\",\n      open: isAddMemberModalVisible,\n      onCancel: () => setIsAddMemberModalVisible(false),\n      onOk: handleAddMembers,\n      okText: \"Add Selected Members\",\n      width: 600,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16\n        },\n        children: /*#__PURE__*/_jsxDEV(Search, {\n          placeholder: \"Search users...\",\n          allowClear: true,\n          value: searchText,\n          onChange: e => setSearchText(e.target.value),\n          style: {\n            width: \"100%\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1005,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1004,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        mode: \"multiple\",\n        placeholder: \"Select users to add\",\n        value: selectedUsers,\n        onChange: setSelectedUsers,\n        style: {\n          width: \"100%\",\n          marginBottom: 16\n        },\n        showSearch: true,\n        filterOption: (input, option) => {\n          const children = option === null || option === void 0 ? void 0 : option.children;\n          if (!children) return false;\n          return children.toString().toLowerCase().includes((input === null || input === void 0 ? void 0 : input.toLowerCase()) || \"\");\n        },\n        children: availableUsers.map(user => /*#__PURE__*/_jsxDEV(Option, {\n          value: user.id,\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              src: user === null || user === void 0 ? void 0 : user.image,\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1033,\n              columnNumber: 17\n            }, this), (user === null || user === void 0 ? void 0 : user.username) || \"Unknown User\", \" (\", (user === null || user === void 0 ? void 0 : user.email) || \"No email\", \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1032,\n            columnNumber: 15\n          }, this)\n        }, user.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1031,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1014,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        children: [selectedUsers.length, \" user(s) selected\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1040,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 996,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"Delete Messages\",\n      open: deleteModalVisible,\n      onCancel: () => {\n        setDeleteModalVisible(false);\n        setDateRange(null);\n        setSelectedUserId(\"\");\n        setSelectedMessageId(\"\");\n      },\n      onOk: handleConfirmDelete,\n      confirmLoading: deletingMessages,\n      okText: \"Delete\",\n      okButtonProps: {\n        danger: true\n      },\n      children: [deleteType === \"single\" && /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Are you sure you want to delete this message? This action cannot be undone.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1059,\n        columnNumber: 11\n      }, this), deleteType === \"all\" && /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Are you sure you want to delete ALL messages in this group? This will permanently delete all messages and cannot be undone.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1066,\n        columnNumber: 11\n      }, this), deleteType === \"user\" && /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Are you sure you want to delete ALL messages by this user in this group? This action cannot be undone.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1073,\n        columnNumber: 11\n      }, this), deleteType === \"dateRange\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Select a date range to delete messages:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1081,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DatePicker.RangePicker, {\n          value: dateRange,\n          onChange: setDateRange,\n          style: {\n            width: \"100%\",\n            marginBottom: 16\n          },\n          placeholder: [\"Start Date\", \"End Date\"]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1082,\n          columnNumber: 13\n        }, this), selectedUserId && /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Note:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1091,\n            columnNumber: 17\n          }, this), \" This will delete messages by the selected user within the specified date range.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1090,\n          columnNumber: 15\n        }, this), !selectedUserId && /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Note:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1098,\n            columnNumber: 17\n          }, this), \" This will delete ALL messages in the group within the specified date range.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1097,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1080,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1044,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `Add External Users to ${(group === null || group === void 0 ? void 0 : group.name) || \"Group\"}`,\n      open: isExternalUserModalVisible,\n      onCancel: () => {\n        setIsExternalUserModalVisible(false);\n        setSelectedExternalUsers([]);\n      },\n      onOk: handleSaveExternalUsers,\n      okText: \"Add Selected Users\",\n      okButtonProps: {\n        disabled: selectedExternalUsers.length === 0\n      },\n      width: 600,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16\n        },\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: [/*#__PURE__*/_jsxDEV(InfoCircleOutlined, {\n            style: {\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1121,\n            columnNumber: 13\n          }, this), \"External users are users who don't belong to this group's assigned batches but can still access the group.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1120,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1119,\n        columnNumber: 9\n      }, this), group && group.batches && group.batches.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16,\n          padding: 12,\n          backgroundColor: \"#f0f8ff\",\n          borderRadius: 6,\n          border: \"1px solid #d6f7ff\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          style: {\n            fontSize: 13\n          },\n          children: [\"Group Batches:\", \" \", group.batches.map(b => b.batchNo).join(\", \")]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1137,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1141,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          style: {\n            fontSize: 12\n          },\n          children: \"Showing users from OTHER batches or users without batch assignments\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1142,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1128,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        mode: \"multiple\",\n        placeholder: \"Select external users to add\",\n        style: {\n          width: \"100%\"\n        },\n        value: selectedExternalUsers,\n        onChange: setSelectedExternalUsers,\n        showSearch: true,\n        filterOption: (input, option) => {\n          var _option$label;\n          return ((_option$label = option === null || option === void 0 ? void 0 : option.label) !== null && _option$label !== void 0 ? _option$label : \"\").toLowerCase().includes(input.toLowerCase());\n        },\n        options: externalUsers.map(user => ({\n          value: user.id.toString(),\n          label: `${user.username} (${user.email}) - ${user.role}`\n        }))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1149,\n        columnNumber: 9\n      }, this), selectedExternalUsers.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 16,\n          padding: 12,\n          backgroundColor: \"#f6ffed\",\n          borderRadius: 6,\n          border: \"1px solid #b7eb8f\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          style: {\n            color: \"#52c41a\"\n          },\n          children: [selectedExternalUsers.length, \" user(s) selected for addition\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1175,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1166,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1107,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 746,\n    columnNumber: 5\n  }, this);\n};\n_s(GroupDetails, \"4xxKbz27eKp3ePDLMgMF4BsXprc=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = GroupDetails;\nexport default GroupDetails;\nvar _c;\n$RefreshReg$(_c, \"GroupDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Typography", "Input", "Modal", "Select", "message", "Popconfirm", "Tag", "Avatar", "Row", "Col", "Statistic", "Tabs", "DatePicker", "<PERSON><PERSON><PERSON>", "Dropdown", "<PERSON><PERSON>", "ArrowLeftOutlined", "UserAddOutlined", "UserOutlined", "UserDeleteOutlined", "MessageOutlined", "DeleteOutlined", "SearchOutlined", "TeamOutlined", "CalendarOutlined", "StopOutlined", "CheckCircleOutlined", "InfoCircleOutlined", "UnlockOutlined", "useParams", "useNavigate", "apiService", "io", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "Search", "Option", "RangePicker", "TabPane", "GroupDetails", "_s", "groupId", "navigate", "group", "setGroup", "members", "setMembers", "messages", "setMessages", "allUsers", "setAllUsers", "loading", "setLoading", "searchText", "setSearchText", "isAddMemberModalVisible", "setIsAddMemberModalVisible", "selectedUsers", "setSelectedUsers", "activeTab", "setActiveTab", "isExternalUserModalVisible", "setIsExternalUserModalVisible", "externalUsers", "setExternalUsers", "selectedExternalUsers", "setSelectedExternalUsers", "deleteModalVisible", "setDeleteModalVisible", "deleteType", "setDeleteType", "selectedMessageId", "setSelectedMessageId", "selectedUserId", "setSelectedUserId", "date<PERSON><PERSON><PERSON>", "setDateRange", "deletingMessages", "setDeletingMessages", "socket", "setSocket", "fetchGroupDetails", "fetchGroupMembers", "fetchGroupMessages", "fetchAvailableUsers", "newSocket", "transports", "autoConnect", "on", "console", "log", "emit", "deletionData", "prevMessages", "map", "msg", "id", "toString", "isDeleted", "deletedBy", "deletedAt", "bulkDeletionData", "type", "deletedMessageIds", "includes", "parseInt", "disconnect", "response", "getGroupById", "data", "success", "error", "getGroupMembers", "getGroupMessages", "getAvailableUsersForGroup", "users", "groupInfo", "handleAddMembers", "promises", "userId", "addGroupMember", "Promise", "all", "length", "handleRemoveMember", "removeGroupMember", "handleAddExternalUser", "getAllUsers", "_group$batches", "currentMemberIds", "m", "groupBatchIds", "batches", "b", "availableExternalUsers", "filter", "user", "_user$trainee", "trainee", "batchId", "handleSaveExternalUsers", "handleBlockMember", "blockGroupMember", "handleUnblockMember", "unblockGroupMember", "handleDeleteMessage", "messageId", "deleteMessage", "handleDeleteAllMessages", "deleteAllGroupMessages", "handleDeleteMessagesByDateRange", "startDate", "endDate", "deleteGroupMessagesByDateRange", "handleDeleteUserMessages", "deleteAllUserMessages", "handleDeleteUserMessagesByDateRange", "deleteUserMessagesByDateRange", "openDeleteModal", "handleConfirmDelete", "format", "availableUsers", "_user$username", "some", "member", "username", "toLowerCase", "filteredMessages", "_message$content", "_message$user", "_message$user$usernam", "content", "memberColumns", "title", "dataIndex", "key", "render", "children", "src", "image", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "strong", "email", "date", "Date", "toLocaleDateString", "_", "record", "isBlocked", "color", "blockedAt", "toLocaleString", "onConfirm", "okText", "cancelText", "size", "danger", "messageColumns", "_record$user", "_record$user2", "_record$deletedBy", "style", "fontSize", "createdAt", "marginTop", "padding", "backgroundColor", "border", "borderRadius", "fontStyle", "marginRight", "isAdmin", "images", "img", "index", "imageUrl", "alt", "max<PERSON><PERSON><PERSON>", "maxHeight", "text", "_record$user3", "deleteMenu", "<PERSON><PERSON>", "onClick", "undefined", "Divider", "menu", "items", "props", "trigger", "className", "marginBottom", "justify", "align", "level", "margin", "name", "gutter", "span", "value", "prefix", "active<PERSON><PERSON>", "onChange", "tab", "valueStyle", "_member$user", "role", "_member$user2", "Math", "round", "suffix", "placeholder", "allowClear", "width", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "scroll", "x", "e", "target", "open", "onCancel", "onOk", "mode", "showSearch", "filterOption", "input", "option", "confirmLoading", "okButtonProps", "disabled", "batchNo", "join", "_option$label", "label", "options", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/sg-alumni/alumni-web/src/Pages/GroupManagement/GroupDetails.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Typography,\n  Input,\n  Modal,\n  Form,\n  Select,\n  message,\n  Popconfirm,\n  Tag,\n  Avatar,\n  Row,\n  Col,\n  Statistic,\n  Tabs,\n  DatePicker,\n  List,\n  Tooltip,\n  Badge,\n  Dropdown,\n  Menu,\n} from \"antd\";\nimport {\n  ArrowLeftOutlined,\n  UserAddOutlined,\n  UserOutlined,\n  UserDeleteOutlined,\n  MessageOutlined,\n  DeleteOutlined,\n  SearchOutlined,\n  TeamOutlined,\n  CalendarOutlined,\n  ExclamationCircleOutlined,\n  EditOutlined,\n  SendOutlined,\n  StopOutlined,\n  CheckCircleOutlined,\n  InfoCircleOutlined,\n  UnlockOutlined,\n} from \"@ant-design/icons\";\nimport { useParams, useNavigate } from \"react-router-dom\";\nimport { apiService } from \"../../services/apiService\";\nimport { io } from \"socket.io-client\";\nimport \"./GroupManagement.css\";\n\nconst { Title, Text } = Typography;\nconst { Search } = Input;\nconst { Option } = Select;\nconst { RangePicker } = DatePicker;\nconst { TabPane } = Tabs;\n\ninterface GroupMember {\n  id: string;\n  userId: string;\n  user: {\n    id: string;\n    username: string;\n    email: string;\n    image: string;\n    role?: string;\n  };\n  joinedAt: string;\n  isBlocked?: boolean;\n  blockedBy?: string;\n  blockedAt?: string;\n}\n\ninterface Message {\n  id: string;\n  content: string;\n  userId: string;\n  user: {\n    id: string;\n    username: string;\n    email: string;\n    image: string;\n  };\n  createdAt: string;\n  updatedAt: string;\n  images?: Array<{ imageUrl: string }>;\n  isDeleted?: boolean;\n  deletedBy?: {\n    userId: string;\n    role: string;\n    isAdmin: boolean;\n  };\n  deletedAt?: string;\n}\n\ninterface User {\n  id: string;\n  username: string;\n  email: string;\n  image: string;\n  role?: string;\n}\n\nconst GroupDetails: React.FC = () => {\n  const { groupId } = useParams<{ groupId: string }>();\n  const navigate = useNavigate();\n\n  const [group, setGroup] = useState<any>(null);\n  const [members, setMembers] = useState<GroupMember[]>([]);\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [allUsers, setAllUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [searchText, setSearchText] = useState(\"\");\n  const [isAddMemberModalVisible, setIsAddMemberModalVisible] = useState(false);\n  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);\n  const [activeTab, setActiveTab] = useState(\"members\");\n  const [isExternalUserModalVisible, setIsExternalUserModalVisible] =\n    useState(false);\n  const [externalUsers, setExternalUsers] = useState<User[]>([]);\n  const [selectedExternalUsers, setSelectedExternalUsers] = useState<string[]>(\n    []\n  );\n\n  // Message deletion states\n  const [deleteModalVisible, setDeleteModalVisible] = useState(false);\n  const [deleteType, setDeleteType] = useState<\n    \"single\" | \"all\" | \"user\" | \"dateRange\"\n  >(\"single\");\n  const [selectedMessageId, setSelectedMessageId] = useState<string>(\"\");\n  const [selectedUserId, setSelectedUserId] = useState<string>(\"\");\n  const [dateRange, setDateRange] = useState<[any, any] | null>(null);\n  const [deletingMessages, setDeletingMessages] = useState(false);\n  const [socket, setSocket] = useState<any>(null);\n\n  useEffect(() => {\n    if (groupId) {\n      fetchGroupDetails();\n      fetchGroupMembers();\n      fetchGroupMessages();\n      fetchAvailableUsers();\n    }\n  }, [groupId]);\n\n  // Socket connection for real-time message deletion updates\n  useEffect(() => {\n    if (!groupId) return;\n\n    const newSocket = io(\"http://192.168.1.49:3008\", {\n      transports: [\"websocket\"],\n      autoConnect: true,\n    });\n\n    newSocket.on(\"connect\", () => {\n      console.log(\"🔗 Connected to message server\");\n      newSocket.emit(\"joinGroup\", groupId);\n    });\n\n    // Listen for single message deletion\n    newSocket.on(\"messageDeleted\", (deletionData: any) => {\n      console.log(\"📧 Message deleted:\", deletionData);\n      setMessages((prevMessages) =>\n        prevMessages.map((msg) =>\n          msg.id === deletionData.id.toString()\n            ? {\n                ...msg,\n                isDeleted: true,\n                deletedBy: deletionData.deletedBy,\n                deletedAt: deletionData.deletedAt,\n              }\n            : msg\n        )\n      );\n    });\n\n    // Listen for bulk message deletion\n    newSocket.on(\"messagesBulkDeleted\", (bulkDeletionData: any) => {\n      console.log(\"📧 Bulk messages deleted:\", bulkDeletionData);\n\n      if (bulkDeletionData.type === \"bulk_delete_all\") {\n        // Mark all messages as deleted\n        setMessages((prevMessages) =>\n          prevMessages.map((msg) => ({\n            ...msg,\n            isDeleted: true,\n            deletedBy: bulkDeletionData.deletedBy,\n            deletedAt: bulkDeletionData.deletedAt,\n          }))\n        );\n      } else {\n        // Mark specific messages as deleted\n        setMessages((prevMessages) =>\n          prevMessages.map((msg) =>\n            bulkDeletionData.deletedMessageIds.includes(parseInt(msg.id))\n              ? {\n                  ...msg,\n                  isDeleted: true,\n                  deletedBy: bulkDeletionData.deletedBy,\n                  deletedAt: bulkDeletionData.deletedAt,\n                }\n              : msg\n          )\n        );\n      }\n    });\n\n    setSocket(newSocket);\n\n    return () => {\n      newSocket.disconnect();\n    };\n  }, [groupId]);\n\n  const fetchGroupDetails = async () => {\n    try {\n      const response = await apiService.getGroupById(groupId!);\n      if (response.data.success) {\n        setGroup(response.data.group);\n      }\n    } catch (error) {\n      console.error(\"Error fetching group details:\", error);\n      message.error(\"Failed to fetch group details\");\n    }\n  };\n\n  const fetchGroupMembers = async () => {\n    setLoading(true);\n    try {\n      const response = await apiService.getGroupMembers(groupId!);\n      if (response.data.success) {\n        setMembers(response.data.members || []);\n      }\n    } catch (error) {\n      console.error(\"Error fetching group members:\", error);\n      message.error(\"Failed to fetch group members\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchGroupMessages = async () => {\n    setLoading(true);\n    try {\n      const response = await apiService.getGroupMessages(groupId!);\n      console.log(response);\n\n      if (response) {\n        setMessages(response.data || []);\n      }\n    } catch (error) {\n      console.error(\"Error fetching group messages:\", error);\n      message.error(\"Failed to fetch group messages\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchAvailableUsers = async () => {\n    if (!groupId) return;\n\n    try {\n      console.log(\"🔍 Fetching available users for group:\", groupId);\n      const response = await apiService.getAvailableUsersForGroup(groupId);\n      if (response.data.success) {\n        console.log(\"👥 Available users:\", response.data.users);\n        console.log(\"📋 Group info:\", response.data.groupInfo);\n        setAllUsers(response.data.users || []);\n      }\n    } catch (error) {\n      console.error(\"Error fetching available users:\", error);\n      message.error(\"Failed to fetch available users\");\n    }\n  };\n\n  const handleAddMembers = async () => {\n    try {\n      const promises = selectedUsers.map((userId) =>\n        apiService.addGroupMember({ userId, groupId: groupId! })\n      );\n\n      await Promise.all(promises);\n      message.success(`${selectedUsers.length} member(s) added successfully`);\n      setIsAddMemberModalVisible(false);\n      setSelectedUsers([]);\n      fetchGroupMembers();\n    } catch (error) {\n      console.error(\"Error adding members:\", error);\n      message.error(\"Failed to add members\");\n    }\n  };\n\n  const handleRemoveMember = async (userId: string) => {\n    try {\n      await apiService.removeGroupMember(userId, groupId!);\n      message.success(\"Member removed successfully\");\n      fetchGroupMembers();\n    } catch (error) {\n      console.error(\"Error removing member:\", error);\n      message.error(\"Failed to remove member\");\n    }\n  };\n\n  const handleAddExternalUser = async () => {\n    if (!groupId) return;\n\n    try {\n      // Fetch all users for external selection\n      const response = await apiService.getAllUsers();\n      if (response.data.success) {\n        const currentMemberIds = members.map((m) => m.id);\n        const groupBatchIds = group?.batches?.map((b: any) => b.id) || [];\n\n        // Filter to show only external users (not in group's batches and not current members)\n        const availableExternalUsers = response.data.users.filter(\n          (user: any) => {\n            // Exclude current members\n            if (currentMemberIds.includes(user.id)) return false;\n\n            // If group has specific batches, only show users NOT from those batches\n            if (groupBatchIds.length > 0) {\n              return (\n                !user.trainee?.batchId ||\n                !groupBatchIds.includes(user.trainee.batchId)\n              );\n            }\n\n            // If group is universal, show all non-members\n            return true;\n          }\n        );\n\n        setExternalUsers(availableExternalUsers);\n        setSelectedExternalUsers([]);\n        setIsExternalUserModalVisible(true);\n      }\n    } catch (error) {\n      console.error(\"Error fetching external users:\", error);\n      message.error(\"Failed to fetch external users\");\n    }\n  };\n\n  const handleSaveExternalUsers = async () => {\n    if (!groupId || selectedExternalUsers.length === 0) return;\n\n    try {\n      const promises = selectedExternalUsers.map((userId) =>\n        apiService.addGroupMember({ userId, groupId: groupId! })\n      );\n\n      await Promise.all(promises);\n      message.success(\n        `${selectedExternalUsers.length} external user(s) added successfully`\n      );\n      setIsExternalUserModalVisible(false);\n      setSelectedExternalUsers([]);\n      fetchGroupMembers();\n    } catch (error) {\n      console.error(\"Error adding external users:\", error);\n      message.error(\"Failed to add external users\");\n    }\n  };\n\n  const handleBlockMember = async (userId: string) => {\n    try {\n      await apiService.blockGroupMember(userId, groupId!);\n      message.success(\"Member blocked from messaging successfully\");\n      fetchGroupMembers();\n    } catch (error) {\n      console.error(\"Error blocking member:\", error);\n      message.error(\"Failed to block member\");\n    }\n  };\n\n  const handleUnblockMember = async (userId: string) => {\n    try {\n      await apiService.unblockGroupMember(userId, groupId!);\n      message.success(\"Member unblocked successfully\");\n      fetchGroupMembers();\n    } catch (error) {\n      console.error(\"Error unblocking member:\", error);\n      message.error(\"Failed to unblock member\");\n    }\n  };\n\n  const handleDeleteMessage = async (messageId: string) => {\n    try {\n      await apiService.deleteMessage(messageId);\n      message.success(\"Message deleted successfully\");\n      fetchGroupMessages();\n    } catch (error) {\n      console.error(\"Error deleting message:\", error);\n      message.error(\"Failed to delete message\");\n    }\n  };\n\n  const handleDeleteAllMessages = async () => {\n    try {\n      await apiService.deleteAllGroupMessages(groupId!);\n      message.success(\"All messages deleted successfully\");\n      fetchGroupMessages();\n    } catch (error) {\n      console.error(\"Error deleting all messages:\", error);\n      message.error(\"Failed to delete all messages\");\n    }\n  };\n\n  const handleDeleteMessagesByDateRange = async (\n    startDate: string,\n    endDate: string\n  ) => {\n    try {\n      await apiService.deleteGroupMessagesByDateRange(\n        groupId!,\n        startDate,\n        endDate\n      );\n      message.success(\"Messages deleted successfully\");\n      fetchGroupMessages();\n    } catch (error) {\n      console.error(\"Error deleting messages by date range:\", error);\n      message.error(\"Failed to delete messages\");\n    }\n  };\n\n  // New functions for enhanced message deletion\n  const handleDeleteUserMessages = async (userId: string) => {\n    try {\n      setDeletingMessages(true);\n      await apiService.deleteAllUserMessages(userId);\n      message.success(\"All user messages deleted successfully\");\n      fetchGroupMessages();\n    } catch (error) {\n      console.error(\"Error deleting user messages:\", error);\n      message.error(\"Failed to delete user messages\");\n    } finally {\n      setDeletingMessages(false);\n    }\n  };\n\n  const handleDeleteUserMessagesByDateRange = async (\n    userId: string,\n    startDate: string,\n    endDate: string\n  ) => {\n    try {\n      setDeletingMessages(true);\n      await apiService.deleteUserMessagesByDateRange(\n        userId,\n        startDate,\n        endDate\n      );\n      message.success(\"User messages in date range deleted successfully\");\n      fetchGroupMessages();\n    } catch (error) {\n      console.error(\"Error deleting user messages by date range:\", error);\n      message.error(\"Failed to delete user messages\");\n    } finally {\n      setDeletingMessages(false);\n    }\n  };\n\n  const openDeleteModal = (\n    type: \"single\" | \"all\" | \"user\" | \"dateRange\",\n    messageId?: string,\n    userId?: string\n  ) => {\n    setDeleteType(type);\n    setSelectedMessageId(messageId || \"\");\n    setSelectedUserId(userId || \"\");\n    setDeleteModalVisible(true);\n  };\n\n  const handleConfirmDelete = async () => {\n    if (deleteType === \"single\" && selectedMessageId) {\n      await handleDeleteMessage(selectedMessageId);\n    } else if (deleteType === \"all\") {\n      await handleDeleteAllMessages();\n    } else if (deleteType === \"user\" && selectedUserId) {\n      await handleDeleteUserMessages(selectedUserId);\n    } else if (deleteType === \"dateRange\" && dateRange) {\n      const startDate = dateRange[0].format(\"YYYY-MM-DD\");\n      const endDate = dateRange[1].format(\"YYYY-MM-DD\");\n\n      if (selectedUserId) {\n        await handleDeleteUserMessagesByDateRange(\n          selectedUserId,\n          startDate,\n          endDate\n        );\n      } else {\n        await handleDeleteMessagesByDateRange(startDate, endDate);\n      }\n    }\n\n    setDeleteModalVisible(false);\n    setDateRange(null);\n    setSelectedUserId(\"\");\n    setSelectedMessageId(\"\");\n  };\n\n  const availableUsers = allUsers.filter(\n    (user) =>\n      !members.some((member) => member.userId === user.id) &&\n      user.username?.toLowerCase().includes(searchText.toLowerCase())\n  );\n\n  const filteredMessages = messages.filter(\n    (message) =>\n      message.content?.toLowerCase().includes(searchText.toLowerCase()) ||\n      message.user?.username?.toLowerCase().includes(searchText.toLowerCase())\n  );\n\n  const memberColumns = [\n    {\n      title: \"Member\",\n      dataIndex: \"user\",\n      key: \"user\",\n      render: (user: any) => (\n        <Space>\n          <Avatar src={user?.image} icon={<TeamOutlined />} />\n          <div>\n            <Text strong>{user?.username || \"Unknown User\"}</Text>\n            <br />\n            <Text type=\"secondary\">{user?.email || \"No email\"}</Text>\n          </div>\n        </Space>\n      ),\n    },\n    {\n      title: \"Joined\",\n      dataIndex: \"joinedAt\",\n      key: \"joinedAt\",\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: \"Status\",\n      key: \"status\",\n      render: (_: any, record: GroupMember) => (\n        <Space>\n          {record.isBlocked ? (\n            <Tag color=\"red\" icon={<StopOutlined />}>\n              Blocked\n            </Tag>\n          ) : (\n            <Tag color=\"green\" icon={<CheckCircleOutlined />}>\n              Active\n            </Tag>\n          )}\n          {record.blockedAt && (\n            <Tooltip\n              title={`Blocked on ${new Date(\n                record.blockedAt\n              ).toLocaleString()}`}\n            >\n              <InfoCircleOutlined />\n            </Tooltip>\n          )}\n        </Space>\n      ),\n    },\n    {\n      title: \"Actions\",\n      key: \"actions\",\n      render: (_: any, record: GroupMember) => (\n        <Space>\n          {record.isBlocked ? (\n            <Popconfirm\n              title=\"Are you sure you want to unblock this member? They will be able to send messages again.\"\n              onConfirm={() => handleUnblockMember(record.userId)}\n              okText=\"Yes\"\n              cancelText=\"No\"\n            >\n              <Button type=\"primary\" icon={<UnlockOutlined />} size=\"small\">\n                Unblock\n              </Button>\n            </Popconfirm>\n          ) : (\n            <Popconfirm\n              title=\"Are you sure you want to block this member? They will not be able to send messages in this group.\"\n              onConfirm={() => handleBlockMember(record.userId)}\n              okText=\"Yes\"\n              cancelText=\"No\"\n            >\n              <Button icon={<StopOutlined />} size=\"small\">\n                Block\n              </Button>\n            </Popconfirm>\n          )}\n          <Popconfirm\n            title=\"Are you sure you want to remove this member from the group? This action cannot be undone.\"\n            onConfirm={() => handleRemoveMember(record.userId)}\n            okText=\"Yes\"\n            cancelText=\"No\"\n          >\n            <Button danger icon={<UserDeleteOutlined />} size=\"small\">\n              Remove\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  const messageColumns = [\n    {\n      title: \"Message\",\n      dataIndex: \"content\",\n      key: \"content\",\n      render: (content: string, record: Message) => (\n        <div>\n          <Space>\n            <Avatar src={record.user?.image} size=\"small\" />\n            <Text strong>{record.user?.username || \"Unknown User\"}</Text>\n            <Text type=\"secondary\" style={{ fontSize: \"12px\" }}>\n              {new Date(record.createdAt).toLocaleString()}\n            </Text>\n            {record.isDeleted && (\n              <Tag color=\"red\" icon={<DeleteOutlined />}>\n                Deleted\n              </Tag>\n            )}\n          </Space>\n          <div style={{ marginTop: 8 }}>\n            {record.isDeleted ? (\n              <div\n                style={{\n                  padding: \"8px 12px\",\n                  backgroundColor: \"#fff2f0\",\n                  border: \"1px solid #ffccc7\",\n                  borderRadius: \"6px\",\n                  fontStyle: \"italic\",\n                }}\n              >\n                <Text type=\"secondary\" style={{ color: \"#cf1322\" }}>\n                  <DeleteOutlined style={{ marginRight: 8 }} />\n                  {record.deletedBy?.isAdmin\n                    ? \"This message was deleted by admin\"\n                    : \"This message was deleted by user\"}\n                </Text>\n                {record.deletedAt && (\n                  <div style={{ marginTop: 4 }}>\n                    <Text\n                      type=\"secondary\"\n                      style={{ fontSize: \"11px\", color: \"#8c8c8c\" }}\n                    >\n                      Deleted on {new Date(record.deletedAt).toLocaleString()}\n                    </Text>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <>\n                <Text>{content || \"No content\"}</Text>\n                {record.images && record.images.length > 0 && (\n                  <div style={{ marginTop: 8 }}>\n                    {record.images.map((img, index) => (\n                      <img\n                        key={index}\n                        src={img.imageUrl}\n                        alt=\"Message attachment\"\n                        style={{\n                          maxWidth: 100,\n                          maxHeight: 100,\n                          marginRight: 8,\n                        }}\n                      />\n                    ))}\n                  </div>\n                )}\n              </>\n            )}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: \"Actions\",\n      key: \"actions\",\n      render: (text: any, record: Message) => {\n        // Don't show actions for deleted messages\n        if (record.isDeleted) {\n          return (\n            <Text type=\"secondary\" style={{ fontStyle: \"italic\" }}>\n              No actions available\n            </Text>\n          );\n        }\n\n        const deleteMenu = (\n          <Menu>\n            <Menu.Item\n              key=\"single\"\n              icon={<DeleteOutlined />}\n              onClick={() => openDeleteModal(\"single\", record.id)}\n            >\n              Delete This Message\n            </Menu.Item>\n            <Menu.Item\n              key=\"user\"\n              icon={<UserDeleteOutlined />}\n              onClick={() => openDeleteModal(\"user\", undefined, record.user.id)}\n            >\n              Delete All Messages by {record.user?.username || \"Unknown User\"}\n            </Menu.Item>\n            <Menu.Divider />\n            <Menu.Item\n              key=\"userDateRange\"\n              icon={<CalendarOutlined />}\n              onClick={() =>\n                openDeleteModal(\"dateRange\", undefined, record.user.id)\n              }\n            >\n              Delete User Messages by Date Range\n            </Menu.Item>\n          </Menu>\n        );\n\n        return (\n          <Space>\n            <Popconfirm\n              title=\"Are you sure you want to delete this message? This action cannot be undone.\"\n              onConfirm={() => handleDeleteMessage(record.id)}\n              okText=\"Yes\"\n              cancelText=\"No\"\n            >\n              <Button danger icon={<DeleteOutlined />} size=\"small\">\n                Delete\n              </Button>\n            </Popconfirm>\n            <Dropdown\n              menu={{ items: deleteMenu.props.children }}\n              trigger={[\"click\"]}\n            >\n              <Button size=\"small\" icon={<DeleteOutlined />}>\n                More Options\n              </Button>\n            </Dropdown>\n          </Space>\n        );\n      },\n    },\n  ];\n\n  if (!group) {\n    return <div>Loading...</div>;\n  }\n\n  return (\n    <div className=\"group-management-container\">\n      {/* Header */}\n      <Card style={{ marginBottom: 24 }}>\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Space>\n              <Button\n                icon={<ArrowLeftOutlined />}\n                onClick={() => navigate(\"/group-management\")}\n              >\n                Back to Group Forums\n              </Button>\n              <Avatar src={group.image} icon={<TeamOutlined />} size=\"large\" />\n              <div>\n                <Title level={3} style={{ margin: 0 }}>\n                  {group.name}\n                </Title>\n                <Text type=\"secondary\">Group Forum Management</Text>\n              </div>\n            </Space>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* Statistics */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col span={8}>\n          <Card>\n            <Statistic\n              title=\"Total Members\"\n              value={members.length}\n              prefix={<TeamOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={8}>\n          <Card>\n            <Statistic\n              title=\"Total Messages\"\n              value={messages.length}\n              prefix={<MessageOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={8}>\n          <Card>\n            <Statistic\n              title=\"Created\"\n              value={new Date(group.createdAt).toLocaleDateString()}\n              prefix={<CalendarOutlined />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Main Content */}\n      <Card>\n        <Tabs activeKey={activeTab} onChange={setActiveTab}>\n          <TabPane tab={`Members (${members.length})`} key=\"members\">\n            {/* Member Statistics */}\n            <Row gutter={16} style={{ marginBottom: 16 }}>\n              <Col span={6}>\n                <Card size=\"small\">\n                  <Statistic\n                    title=\"Total Members\"\n                    value={members.length}\n                    prefix={<TeamOutlined />}\n                    valueStyle={{ color: \"#1890ff\" }}\n                  />\n                </Card>\n              </Col>\n              <Col span={6}>\n                <Card size=\"small\">\n                  <Statistic\n                    title=\"Available Users\"\n                    value={availableUsers.length}\n                    prefix={<UserAddOutlined />}\n                    valueStyle={{ color: \"#52c41a\" }}\n                  />\n                </Card>\n              </Col>\n              <Col span={6}>\n                <Card size=\"small\">\n                  <Statistic\n                    title=\"Admin Members\"\n                    value={\n                      members.filter((member) => member.user?.role === \"ADMIN\")\n                        .length\n                    }\n                    prefix={<TeamOutlined />}\n                    valueStyle={{ color: \"#f5222d\" }}\n                  />\n                </Card>\n              </Col>\n              <Col span={6}>\n                <Card size=\"small\">\n                  <Statistic\n                    title=\"HR Members\"\n                    value={\n                      members.filter((member) => member.user?.role === \"HR\")\n                        .length\n                    }\n                    prefix={<TeamOutlined />}\n                    valueStyle={{ color: \"#fa8c16\" }}\n                  />\n                </Card>\n              </Col>\n            </Row>\n\n            {/* Blocked Members Statistics */}\n            <Row gutter={16} style={{ marginBottom: 16 }}>\n              <Col span={8}>\n                <Card size=\"small\">\n                  <Statistic\n                    title=\"Blocked Members\"\n                    value={members.filter((member) => member.isBlocked).length}\n                    prefix={<StopOutlined />}\n                    valueStyle={{ color: \"#f5222d\" }}\n                  />\n                </Card>\n              </Col>\n              <Col span={8}>\n                <Card size=\"small\">\n                  <Statistic\n                    title=\"Active Members\"\n                    value={members.filter((member) => !member.isBlocked).length}\n                    prefix={<CheckCircleOutlined />}\n                    valueStyle={{ color: \"#52c41a\" }}\n                  />\n                </Card>\n              </Col>\n              <Col span={8}>\n                <Card size=\"small\">\n                  <Statistic\n                    title=\"Block Rate\"\n                    value={\n                      members.length > 0\n                        ? Math.round(\n                            (members.filter((member) => member.isBlocked)\n                              .length /\n                              members.length) *\n                              100\n                          )\n                        : 0\n                    }\n                    suffix=\"%\"\n                    prefix={<InfoCircleOutlined />}\n                    valueStyle={{ color: \"#722ed1\" }}\n                  />\n                </Card>\n              </Col>\n            </Row>\n\n            <div style={{ marginBottom: 16 }}>\n              <Row justify=\"space-between\" align=\"middle\">\n                <Col>\n                  <Search\n                    placeholder=\"Search members...\"\n                    allowClear\n                    style={{ width: 300 }}\n                    prefix={<SearchOutlined />}\n                  />\n                </Col>\n                <Col>\n                  <Space>\n                    <Text type=\"secondary\">\n                      Showing {members.length} of{\" \"}\n                      {members.length + availableUsers.length} total users\n                    </Text>\n                    <Button\n                      type=\"primary\"\n                      icon={<UserAddOutlined />}\n                      onClick={() => setIsAddMemberModalVisible(true)}\n                    >\n                      Add Members\n                    </Button>\n                    <Button\n                      type=\"default\"\n                      icon={<UserOutlined />}\n                      onClick={handleAddExternalUser}\n                    >\n                      Add External User\n                    </Button>\n                  </Space>\n                </Col>\n              </Row>\n            </div>\n\n            <Table\n              columns={memberColumns}\n              dataSource={members}\n              rowKey=\"id\"\n              loading={loading}\n              pagination={{ pageSize: 10 }}\n              scroll={{ x: \"max-content\" }}\n              size=\"middle\"\n            />\n          </TabPane>\n\n          <TabPane tab={`Messages (${messages.length})`} key=\"messages\">\n            <div style={{ marginBottom: 16 }}>\n              <Row justify=\"space-between\" align=\"middle\">\n                <Col>\n                  <Search\n                    placeholder=\"Search messages...\"\n                    allowClear\n                    value={searchText}\n                    onChange={(e) => setSearchText(e.target.value)}\n                    style={{ width: 300 }}\n                    prefix={<SearchOutlined />}\n                  />\n                </Col>\n                <Col>\n                  <Space>\n                    <Popconfirm\n                      title=\"Are you sure you want to delete all messages? This will permanently delete all messages in this group and cannot be undone.\"\n                      onConfirm={handleDeleteAllMessages}\n                      okText=\"Yes\"\n                      cancelText=\"No\"\n                    >\n                      <Button danger icon={<DeleteOutlined />}>\n                        Delete All Messages\n                      </Button>\n                    </Popconfirm>\n\n                    <Button\n                      icon={<CalendarOutlined />}\n                      onClick={() => openDeleteModal(\"dateRange\")}\n                    >\n                      Delete by Date Range\n                    </Button>\n                  </Space>\n                </Col>\n              </Row>\n            </div>\n\n            <Table\n              columns={messageColumns}\n              dataSource={filteredMessages}\n              rowKey=\"id\"\n              loading={loading}\n              pagination={{ pageSize: 10 }}\n              scroll={{ x: \"max-content\" }}\n              size=\"middle\"\n            />\n          </TabPane>\n        </Tabs>\n      </Card>\n\n      {/* Add Members Modal */}\n      <Modal\n        title=\"Add Members to Group\"\n        open={isAddMemberModalVisible}\n        onCancel={() => setIsAddMemberModalVisible(false)}\n        onOk={handleAddMembers}\n        okText=\"Add Selected Members\"\n        width={600}\n      >\n        <div style={{ marginBottom: 16 }}>\n          <Search\n            placeholder=\"Search users...\"\n            allowClear\n            value={searchText}\n            onChange={(e) => setSearchText(e.target.value)}\n            style={{ width: \"100%\" }}\n          />\n        </div>\n\n        <Select\n          mode=\"multiple\"\n          placeholder=\"Select users to add\"\n          value={selectedUsers}\n          onChange={setSelectedUsers}\n          style={{ width: \"100%\", marginBottom: 16 }}\n          showSearch\n          filterOption={(input, option) => {\n            const children = option?.children;\n            if (!children) return false;\n            return children\n              .toString()\n              .toLowerCase()\n              .includes(input?.toLowerCase() || \"\");\n          }}\n        >\n          {availableUsers.map((user) => (\n            <Option key={user.id} value={user.id}>\n              <Space>\n                <Avatar src={user?.image} size=\"small\" />\n                {user?.username || \"Unknown User\"} ({user?.email || \"No email\"})\n              </Space>\n            </Option>\n          ))}\n        </Select>\n\n        <Text type=\"secondary\">{selectedUsers.length} user(s) selected</Text>\n      </Modal>\n\n      {/* Enhanced Delete Modal */}\n      <Modal\n        title=\"Delete Messages\"\n        open={deleteModalVisible}\n        onCancel={() => {\n          setDeleteModalVisible(false);\n          setDateRange(null);\n          setSelectedUserId(\"\");\n          setSelectedMessageId(\"\");\n        }}\n        onOk={handleConfirmDelete}\n        confirmLoading={deletingMessages}\n        okText=\"Delete\"\n        okButtonProps={{ danger: true }}\n      >\n        {deleteType === \"single\" && (\n          <p>\n            Are you sure you want to delete this message? This action cannot be\n            undone.\n          </p>\n        )}\n\n        {deleteType === \"all\" && (\n          <p>\n            Are you sure you want to delete ALL messages in this group? This\n            will permanently delete all messages and cannot be undone.\n          </p>\n        )}\n\n        {deleteType === \"user\" && (\n          <p>\n            Are you sure you want to delete ALL messages by this user in this\n            group? This action cannot be undone.\n          </p>\n        )}\n\n        {deleteType === \"dateRange\" && (\n          <div>\n            <p>Select a date range to delete messages:</p>\n            <DatePicker.RangePicker\n              value={dateRange}\n              onChange={setDateRange}\n              style={{ width: \"100%\", marginBottom: 16 }}\n              placeholder={[\"Start Date\", \"End Date\"]}\n            />\n\n            {selectedUserId && (\n              <p>\n                <strong>Note:</strong> This will delete messages by the selected\n                user within the specified date range.\n              </p>\n            )}\n\n            {!selectedUserId && (\n              <p>\n                <strong>Note:</strong> This will delete ALL messages in the\n                group within the specified date range.\n              </p>\n            )}\n          </div>\n        )}\n      </Modal>\n\n      {/* External User Modal */}\n      <Modal\n        title={`Add External Users to ${group?.name || \"Group\"}`}\n        open={isExternalUserModalVisible}\n        onCancel={() => {\n          setIsExternalUserModalVisible(false);\n          setSelectedExternalUsers([]);\n        }}\n        onOk={handleSaveExternalUsers}\n        okText=\"Add Selected Users\"\n        okButtonProps={{ disabled: selectedExternalUsers.length === 0 }}\n        width={600}\n      >\n        <div style={{ marginBottom: 16 }}>\n          <Text type=\"secondary\">\n            <InfoCircleOutlined style={{ marginRight: 8 }} />\n            External users are users who don't belong to this group's assigned\n            batches but can still access the group.\n          </Text>\n        </div>\n\n        {group && group.batches && group.batches.length > 0 && (\n          <div\n            style={{\n              marginBottom: 16,\n              padding: 12,\n              backgroundColor: \"#f0f8ff\",\n              borderRadius: 6,\n              border: \"1px solid #d6f7ff\",\n            }}\n          >\n            <Text strong style={{ fontSize: 13 }}>\n              Group Batches:{\" \"}\n              {group.batches.map((b: any) => b.batchNo).join(\", \")}\n            </Text>\n            <br />\n            <Text type=\"secondary\" style={{ fontSize: 12 }}>\n              Showing users from OTHER batches or users without batch\n              assignments\n            </Text>\n          </div>\n        )}\n\n        <Select\n          mode=\"multiple\"\n          placeholder=\"Select external users to add\"\n          style={{ width: \"100%\" }}\n          value={selectedExternalUsers}\n          onChange={setSelectedExternalUsers}\n          showSearch\n          filterOption={(input, option) =>\n            (option?.label ?? \"\").toLowerCase().includes(input.toLowerCase())\n          }\n          options={externalUsers.map((user) => ({\n            value: user.id.toString(),\n            label: `${user.username} (${user.email}) - ${user.role}`,\n          }))}\n        />\n\n        {selectedExternalUsers.length > 0 && (\n          <div\n            style={{\n              marginTop: 16,\n              padding: 12,\n              backgroundColor: \"#f6ffed\",\n              borderRadius: 6,\n              border: \"1px solid #b7eb8f\",\n            }}\n          >\n            <Text strong style={{ color: \"#52c41a\" }}>\n              {selectedExternalUsers.length} user(s) selected for addition\n            </Text>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default GroupDetails;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,KAAK,EACLC,KAAK,EAELC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,GAAG,EACHC,MAAM,EACNC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,IAAI,EACJC,UAAU,EAEVC,OAAO,EAEPC,QAAQ,EACRC,IAAI,QACC,MAAM;AACb,SACEC,iBAAiB,EACjBC,eAAe,EACfC,YAAY,EACZC,kBAAkB,EAClBC,eAAe,EACfC,cAAc,EACdC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAIhBC,YAAY,EACZC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,QACT,mBAAmB;AAC1B,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,EAAE,QAAQ,kBAAkB;AACrC,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGtC,UAAU;AAClC,MAAM;EAAEuC;AAAO,CAAC,GAAGtC,KAAK;AACxB,MAAM;EAAEuC;AAAO,CAAC,GAAGrC,MAAM;AACzB,MAAM;EAAEsC;AAAY,CAAC,GAAG7B,UAAU;AAClC,MAAM;EAAE8B;AAAQ,CAAC,GAAG/B,IAAI;AAgDxB,MAAMgC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM;IAAEC;EAAQ,CAAC,GAAGhB,SAAS,CAAsB,CAAC;EACpD,MAAMiB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,QAAQ,CAAM,IAAI,CAAC;EAC7C,MAAM,CAACuD,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAgB,EAAE,CAAC;EACzD,MAAM,CAACyD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAAC2D,QAAQ,EAAEC,WAAW,CAAC,GAAG5D,QAAQ,CAAS,EAAE,CAAC;EACpD,MAAM,CAAC6D,OAAO,EAAEC,UAAU,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+D,UAAU,EAAEC,aAAa,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiE,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM,CAACmE,aAAa,EAAEC,gBAAgB,CAAC,GAAGpE,QAAQ,CAAW,EAAE,CAAC;EAChE,MAAM,CAACqE,SAAS,EAAEC,YAAY,CAAC,GAAGtE,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACuE,0BAA0B,EAAEC,6BAA6B,CAAC,GAC/DxE,QAAQ,CAAC,KAAK,CAAC;EACjB,MAAM,CAACyE,aAAa,EAAEC,gBAAgB,CAAC,GAAG1E,QAAQ,CAAS,EAAE,CAAC;EAC9D,MAAM,CAAC2E,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG5E,QAAQ,CAChE,EACF,CAAC;;EAED;EACA,MAAM,CAAC6E,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC+E,UAAU,EAAEC,aAAa,CAAC,GAAGhF,QAAQ,CAE1C,QAAQ,CAAC;EACX,MAAM,CAACiF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlF,QAAQ,CAAS,EAAE,CAAC;EACtE,MAAM,CAACmF,cAAc,EAAEC,iBAAiB,CAAC,GAAGpF,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAACqF,SAAS,EAAEC,YAAY,CAAC,GAAGtF,QAAQ,CAAoB,IAAI,CAAC;EACnE,MAAM,CAACuF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxF,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACyF,MAAM,EAAEC,SAAS,CAAC,GAAG1F,QAAQ,CAAM,IAAI,CAAC;EAE/CC,SAAS,CAAC,MAAM;IACd,IAAIkD,OAAO,EAAE;MACXwC,iBAAiB,CAAC,CAAC;MACnBC,iBAAiB,CAAC,CAAC;MACnBC,kBAAkB,CAAC,CAAC;MACpBC,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAAC3C,OAAO,CAAC,CAAC;;EAEb;EACAlD,SAAS,CAAC,MAAM;IACd,IAAI,CAACkD,OAAO,EAAE;IAEd,MAAM4C,SAAS,GAAGzD,EAAE,CAAC,0BAA0B,EAAE;MAC/C0D,UAAU,EAAE,CAAC,WAAW,CAAC;MACzBC,WAAW,EAAE;IACf,CAAC,CAAC;IAEFF,SAAS,CAACG,EAAE,CAAC,SAAS,EAAE,MAAM;MAC5BC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC7CL,SAAS,CAACM,IAAI,CAAC,WAAW,EAAElD,OAAO,CAAC;IACtC,CAAC,CAAC;;IAEF;IACA4C,SAAS,CAACG,EAAE,CAAC,gBAAgB,EAAGI,YAAiB,IAAK;MACpDH,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEE,YAAY,CAAC;MAChD5C,WAAW,CAAE6C,YAAY,IACvBA,YAAY,CAACC,GAAG,CAAEC,GAAG,IACnBA,GAAG,CAACC,EAAE,KAAKJ,YAAY,CAACI,EAAE,CAACC,QAAQ,CAAC,CAAC,GACjC;QACE,GAAGF,GAAG;QACNG,SAAS,EAAE,IAAI;QACfC,SAAS,EAAEP,YAAY,CAACO,SAAS;QACjCC,SAAS,EAAER,YAAY,CAACQ;MAC1B,CAAC,GACDL,GACN,CACF,CAAC;IACH,CAAC,CAAC;;IAEF;IACAV,SAAS,CAACG,EAAE,CAAC,qBAAqB,EAAGa,gBAAqB,IAAK;MAC7DZ,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEW,gBAAgB,CAAC;MAE1D,IAAIA,gBAAgB,CAACC,IAAI,KAAK,iBAAiB,EAAE;QAC/C;QACAtD,WAAW,CAAE6C,YAAY,IACvBA,YAAY,CAACC,GAAG,CAAEC,GAAG,KAAM;UACzB,GAAGA,GAAG;UACNG,SAAS,EAAE,IAAI;UACfC,SAAS,EAAEE,gBAAgB,CAACF,SAAS;UACrCC,SAAS,EAAEC,gBAAgB,CAACD;QAC9B,CAAC,CAAC,CACJ,CAAC;MACH,CAAC,MAAM;QACL;QACApD,WAAW,CAAE6C,YAAY,IACvBA,YAAY,CAACC,GAAG,CAAEC,GAAG,IACnBM,gBAAgB,CAACE,iBAAiB,CAACC,QAAQ,CAACC,QAAQ,CAACV,GAAG,CAACC,EAAE,CAAC,CAAC,GACzD;UACE,GAAGD,GAAG;UACNG,SAAS,EAAE,IAAI;UACfC,SAAS,EAAEE,gBAAgB,CAACF,SAAS;UACrCC,SAAS,EAAEC,gBAAgB,CAACD;QAC9B,CAAC,GACDL,GACN,CACF,CAAC;MACH;IACF,CAAC,CAAC;IAEFf,SAAS,CAACK,SAAS,CAAC;IAEpB,OAAO,MAAM;MACXA,SAAS,CAACqB,UAAU,CAAC,CAAC;IACxB,CAAC;EACH,CAAC,EAAE,CAACjE,OAAO,CAAC,CAAC;EAEb,MAAMwC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAM0B,QAAQ,GAAG,MAAMhF,UAAU,CAACiF,YAAY,CAACnE,OAAQ,CAAC;MACxD,IAAIkE,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBlE,QAAQ,CAAC+D,QAAQ,CAACE,IAAI,CAAClE,KAAK,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOoE,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD/G,OAAO,CAAC+G,KAAK,CAAC,+BAA+B,CAAC;IAChD;EACF,CAAC;EAED,MAAM7B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC9B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMuD,QAAQ,GAAG,MAAMhF,UAAU,CAACqF,eAAe,CAACvE,OAAQ,CAAC;MAC3D,IAAIkE,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBhE,UAAU,CAAC6D,QAAQ,CAACE,IAAI,CAAChE,OAAO,IAAI,EAAE,CAAC;MACzC;IACF,CAAC,CAAC,OAAOkE,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD/G,OAAO,CAAC+G,KAAK,CAAC,+BAA+B,CAAC;IAChD,CAAC,SAAS;MACR3D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+B,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC/B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMuD,QAAQ,GAAG,MAAMhF,UAAU,CAACsF,gBAAgB,CAACxE,OAAQ,CAAC;MAC5DgD,OAAO,CAACC,GAAG,CAACiB,QAAQ,CAAC;MAErB,IAAIA,QAAQ,EAAE;QACZ3D,WAAW,CAAC2D,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;MAClC;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD/G,OAAO,CAAC+G,KAAK,CAAC,gCAAgC,CAAC;IACjD,CAAC,SAAS;MACR3D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAAC3C,OAAO,EAAE;IAEd,IAAI;MACFgD,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEjD,OAAO,CAAC;MAC9D,MAAMkE,QAAQ,GAAG,MAAMhF,UAAU,CAACuF,yBAAyB,CAACzE,OAAO,CAAC;MACpE,IAAIkE,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBrB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEiB,QAAQ,CAACE,IAAI,CAACM,KAAK,CAAC;QACvD1B,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEiB,QAAQ,CAACE,IAAI,CAACO,SAAS,CAAC;QACtDlE,WAAW,CAACyD,QAAQ,CAACE,IAAI,CAACM,KAAK,IAAI,EAAE,CAAC;MACxC;IACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD/G,OAAO,CAAC+G,KAAK,CAAC,iCAAiC,CAAC;IAClD;EACF,CAAC;EAED,MAAMM,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMC,QAAQ,GAAG7D,aAAa,CAACqC,GAAG,CAAEyB,MAAM,IACxC5F,UAAU,CAAC6F,cAAc,CAAC;QAAED,MAAM;QAAE9E,OAAO,EAAEA;MAAS,CAAC,CACzD,CAAC;MAED,MAAMgF,OAAO,CAACC,GAAG,CAACJ,QAAQ,CAAC;MAC3BtH,OAAO,CAAC8G,OAAO,CAAC,GAAGrD,aAAa,CAACkE,MAAM,+BAA+B,CAAC;MACvEnE,0BAA0B,CAAC,KAAK,CAAC;MACjCE,gBAAgB,CAAC,EAAE,CAAC;MACpBwB,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C/G,OAAO,CAAC+G,KAAK,CAAC,uBAAuB,CAAC;IACxC;EACF,CAAC;EAED,MAAMa,kBAAkB,GAAG,MAAOL,MAAc,IAAK;IACnD,IAAI;MACF,MAAM5F,UAAU,CAACkG,iBAAiB,CAACN,MAAM,EAAE9E,OAAQ,CAAC;MACpDzC,OAAO,CAAC8G,OAAO,CAAC,6BAA6B,CAAC;MAC9C5B,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C/G,OAAO,CAAC+G,KAAK,CAAC,yBAAyB,CAAC;IAC1C;EACF,CAAC;EAED,MAAMe,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,CAACrF,OAAO,EAAE;IAEd,IAAI;MACF;MACA,MAAMkE,QAAQ,GAAG,MAAMhF,UAAU,CAACoG,WAAW,CAAC,CAAC;MAC/C,IAAIpB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QAAA,IAAAkB,cAAA;QACzB,MAAMC,gBAAgB,GAAGpF,OAAO,CAACiD,GAAG,CAAEoC,CAAC,IAAKA,CAAC,CAAClC,EAAE,CAAC;QACjD,MAAMmC,aAAa,GAAG,CAAAxF,KAAK,aAALA,KAAK,wBAAAqF,cAAA,GAALrF,KAAK,CAAEyF,OAAO,cAAAJ,cAAA,uBAAdA,cAAA,CAAgBlC,GAAG,CAAEuC,CAAM,IAAKA,CAAC,CAACrC,EAAE,CAAC,KAAI,EAAE;;QAEjE;QACA,MAAMsC,sBAAsB,GAAG3B,QAAQ,CAACE,IAAI,CAACM,KAAK,CAACoB,MAAM,CACtDC,IAAS,IAAK;UACb;UACA,IAAIP,gBAAgB,CAACzB,QAAQ,CAACgC,IAAI,CAACxC,EAAE,CAAC,EAAE,OAAO,KAAK;;UAEpD;UACA,IAAImC,aAAa,CAACR,MAAM,GAAG,CAAC,EAAE;YAAA,IAAAc,aAAA;YAC5B,OACE,GAAAA,aAAA,GAACD,IAAI,CAACE,OAAO,cAAAD,aAAA,eAAZA,aAAA,CAAcE,OAAO,KACtB,CAACR,aAAa,CAAC3B,QAAQ,CAACgC,IAAI,CAACE,OAAO,CAACC,OAAO,CAAC;UAEjD;;UAEA;UACA,OAAO,IAAI;QACb,CACF,CAAC;QAED3E,gBAAgB,CAACsE,sBAAsB,CAAC;QACxCpE,wBAAwB,CAAC,EAAE,CAAC;QAC5BJ,6BAA6B,CAAC,IAAI,CAAC;MACrC;IACF,CAAC,CAAC,OAAOiD,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD/G,OAAO,CAAC+G,KAAK,CAAC,gCAAgC,CAAC;IACjD;EACF,CAAC;EAED,MAAM6B,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI,CAACnG,OAAO,IAAIwB,qBAAqB,CAAC0D,MAAM,KAAK,CAAC,EAAE;IAEpD,IAAI;MACF,MAAML,QAAQ,GAAGrD,qBAAqB,CAAC6B,GAAG,CAAEyB,MAAM,IAChD5F,UAAU,CAAC6F,cAAc,CAAC;QAAED,MAAM;QAAE9E,OAAO,EAAEA;MAAS,CAAC,CACzD,CAAC;MAED,MAAMgF,OAAO,CAACC,GAAG,CAACJ,QAAQ,CAAC;MAC3BtH,OAAO,CAAC8G,OAAO,CACb,GAAG7C,qBAAqB,CAAC0D,MAAM,sCACjC,CAAC;MACD7D,6BAA6B,CAAC,KAAK,CAAC;MACpCI,wBAAwB,CAAC,EAAE,CAAC;MAC5BgB,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD/G,OAAO,CAAC+G,KAAK,CAAC,8BAA8B,CAAC;IAC/C;EACF,CAAC;EAED,MAAM8B,iBAAiB,GAAG,MAAOtB,MAAc,IAAK;IAClD,IAAI;MACF,MAAM5F,UAAU,CAACmH,gBAAgB,CAACvB,MAAM,EAAE9E,OAAQ,CAAC;MACnDzC,OAAO,CAAC8G,OAAO,CAAC,4CAA4C,CAAC;MAC7D5B,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C/G,OAAO,CAAC+G,KAAK,CAAC,wBAAwB,CAAC;IACzC;EACF,CAAC;EAED,MAAMgC,mBAAmB,GAAG,MAAOxB,MAAc,IAAK;IACpD,IAAI;MACF,MAAM5F,UAAU,CAACqH,kBAAkB,CAACzB,MAAM,EAAE9E,OAAQ,CAAC;MACrDzC,OAAO,CAAC8G,OAAO,CAAC,+BAA+B,CAAC;MAChD5B,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD/G,OAAO,CAAC+G,KAAK,CAAC,0BAA0B,CAAC;IAC3C;EACF,CAAC;EAED,MAAMkC,mBAAmB,GAAG,MAAOC,SAAiB,IAAK;IACvD,IAAI;MACF,MAAMvH,UAAU,CAACwH,aAAa,CAACD,SAAS,CAAC;MACzClJ,OAAO,CAAC8G,OAAO,CAAC,8BAA8B,CAAC;MAC/C3B,kBAAkB,CAAC,CAAC;IACtB,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C/G,OAAO,CAAC+G,KAAK,CAAC,0BAA0B,CAAC;IAC3C;EACF,CAAC;EAED,MAAMqC,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1C,IAAI;MACF,MAAMzH,UAAU,CAAC0H,sBAAsB,CAAC5G,OAAQ,CAAC;MACjDzC,OAAO,CAAC8G,OAAO,CAAC,mCAAmC,CAAC;MACpD3B,kBAAkB,CAAC,CAAC;IACtB,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD/G,OAAO,CAAC+G,KAAK,CAAC,+BAA+B,CAAC;IAChD;EACF,CAAC;EAED,MAAMuC,+BAA+B,GAAG,MAAAA,CACtCC,SAAiB,EACjBC,OAAe,KACZ;IACH,IAAI;MACF,MAAM7H,UAAU,CAAC8H,8BAA8B,CAC7ChH,OAAO,EACP8G,SAAS,EACTC,OACF,CAAC;MACDxJ,OAAO,CAAC8G,OAAO,CAAC,+BAA+B,CAAC;MAChD3B,kBAAkB,CAAC,CAAC;IACtB,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D/G,OAAO,CAAC+G,KAAK,CAAC,2BAA2B,CAAC;IAC5C;EACF,CAAC;;EAED;EACA,MAAM2C,wBAAwB,GAAG,MAAOnC,MAAc,IAAK;IACzD,IAAI;MACFzC,mBAAmB,CAAC,IAAI,CAAC;MACzB,MAAMnD,UAAU,CAACgI,qBAAqB,CAACpC,MAAM,CAAC;MAC9CvH,OAAO,CAAC8G,OAAO,CAAC,wCAAwC,CAAC;MACzD3B,kBAAkB,CAAC,CAAC;IACtB,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD/G,OAAO,CAAC+G,KAAK,CAAC,gCAAgC,CAAC;IACjD,CAAC,SAAS;MACRjC,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,MAAM8E,mCAAmC,GAAG,MAAAA,CAC1CrC,MAAc,EACdgC,SAAiB,EACjBC,OAAe,KACZ;IACH,IAAI;MACF1E,mBAAmB,CAAC,IAAI,CAAC;MACzB,MAAMnD,UAAU,CAACkI,6BAA6B,CAC5CtC,MAAM,EACNgC,SAAS,EACTC,OACF,CAAC;MACDxJ,OAAO,CAAC8G,OAAO,CAAC,kDAAkD,CAAC;MACnE3B,kBAAkB,CAAC,CAAC;IACtB,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnE/G,OAAO,CAAC+G,KAAK,CAAC,gCAAgC,CAAC;IACjD,CAAC,SAAS;MACRjC,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,MAAMgF,eAAe,GAAGA,CACtBxD,IAA6C,EAC7C4C,SAAkB,EAClB3B,MAAe,KACZ;IACHjD,aAAa,CAACgC,IAAI,CAAC;IACnB9B,oBAAoB,CAAC0E,SAAS,IAAI,EAAE,CAAC;IACrCxE,iBAAiB,CAAC6C,MAAM,IAAI,EAAE,CAAC;IAC/BnD,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAM2F,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI1F,UAAU,KAAK,QAAQ,IAAIE,iBAAiB,EAAE;MAChD,MAAM0E,mBAAmB,CAAC1E,iBAAiB,CAAC;IAC9C,CAAC,MAAM,IAAIF,UAAU,KAAK,KAAK,EAAE;MAC/B,MAAM+E,uBAAuB,CAAC,CAAC;IACjC,CAAC,MAAM,IAAI/E,UAAU,KAAK,MAAM,IAAII,cAAc,EAAE;MAClD,MAAMiF,wBAAwB,CAACjF,cAAc,CAAC;IAChD,CAAC,MAAM,IAAIJ,UAAU,KAAK,WAAW,IAAIM,SAAS,EAAE;MAClD,MAAM4E,SAAS,GAAG5E,SAAS,CAAC,CAAC,CAAC,CAACqF,MAAM,CAAC,YAAY,CAAC;MACnD,MAAMR,OAAO,GAAG7E,SAAS,CAAC,CAAC,CAAC,CAACqF,MAAM,CAAC,YAAY,CAAC;MAEjD,IAAIvF,cAAc,EAAE;QAClB,MAAMmF,mCAAmC,CACvCnF,cAAc,EACd8E,SAAS,EACTC,OACF,CAAC;MACH,CAAC,MAAM;QACL,MAAMF,+BAA+B,CAACC,SAAS,EAAEC,OAAO,CAAC;MAC3D;IACF;IAEApF,qBAAqB,CAAC,KAAK,CAAC;IAC5BQ,YAAY,CAAC,IAAI,CAAC;IAClBF,iBAAiB,CAAC,EAAE,CAAC;IACrBF,oBAAoB,CAAC,EAAE,CAAC;EAC1B,CAAC;EAED,MAAMyF,cAAc,GAAGhH,QAAQ,CAACsF,MAAM,CACnCC,IAAI;IAAA,IAAA0B,cAAA;IAAA,OACH,CAACrH,OAAO,CAACsH,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAAC7C,MAAM,KAAKiB,IAAI,CAACxC,EAAE,CAAC,MAAAkE,cAAA,GACpD1B,IAAI,CAAC6B,QAAQ,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,WAAW,CAAC,CAAC,CAAC9D,QAAQ,CAACnD,UAAU,CAACiH,WAAW,CAAC,CAAC,CAAC;EAAA,CACnE,CAAC;EAED,MAAMC,gBAAgB,GAAGxH,QAAQ,CAACwF,MAAM,CACrCvI,OAAO;IAAA,IAAAwK,gBAAA,EAAAC,aAAA,EAAAC,qBAAA;IAAA,OACN,EAAAF,gBAAA,GAAAxK,OAAO,CAAC2K,OAAO,cAAAH,gBAAA,uBAAfA,gBAAA,CAAiBF,WAAW,CAAC,CAAC,CAAC9D,QAAQ,CAACnD,UAAU,CAACiH,WAAW,CAAC,CAAC,CAAC,OAAAG,aAAA,GACjEzK,OAAO,CAACwI,IAAI,cAAAiC,aAAA,wBAAAC,qBAAA,GAAZD,aAAA,CAAcJ,QAAQ,cAAAK,qBAAA,uBAAtBA,qBAAA,CAAwBJ,WAAW,CAAC,CAAC,CAAC9D,QAAQ,CAACnD,UAAU,CAACiH,WAAW,CAAC,CAAC,CAAC;EAAA,CAC5E,CAAC;EAED,MAAMM,aAAa,GAAG,CACpB;IACEC,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAGxC,IAAS,iBAChB1G,OAAA,CAACnC,KAAK;MAAAsL,QAAA,gBACJnJ,OAAA,CAAC3B,MAAM;QAAC+K,GAAG,EAAE1C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2C,KAAM;QAACC,IAAI,eAAEtJ,OAAA,CAACX,YAAY;UAAAkK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpD1J,OAAA;QAAAmJ,QAAA,gBACEnJ,OAAA,CAACI,IAAI;UAACuJ,MAAM;UAAAR,QAAA,EAAE,CAAAzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,QAAQ,KAAI;QAAc;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtD1J,OAAA;UAAAuJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN1J,OAAA,CAACI,IAAI;UAACoE,IAAI,EAAC,WAAW;UAAA2E,QAAA,EAAE,CAAAzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,KAAK,KAAI;QAAU;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEX,CAAC,EACD;IACEX,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGW,IAAY,IAAK,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,kBAAkB,CAAC;EAC9D,CAAC,EACD;IACEhB,KAAK,EAAE,QAAQ;IACfE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACc,CAAM,EAAEC,MAAmB,kBAClCjK,OAAA,CAACnC,KAAK;MAAAsL,QAAA,GACHc,MAAM,CAACC,SAAS,gBACflK,OAAA,CAAC5B,GAAG;QAAC+L,KAAK,EAAC,KAAK;QAACb,IAAI,eAAEtJ,OAAA,CAACT,YAAY;UAAAgK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAP,QAAA,EAAC;MAEzC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAEN1J,OAAA,CAAC5B,GAAG;QAAC+L,KAAK,EAAC,OAAO;QAACb,IAAI,eAAEtJ,OAAA,CAACR,mBAAmB;UAAA+J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAP,QAAA,EAAC;MAElD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN,EACAO,MAAM,CAACG,SAAS,iBACfpK,OAAA,CAACrB,OAAO;QACNoK,KAAK,EAAE,cAAc,IAAIe,IAAI,CAC3BG,MAAM,CAACG,SACT,CAAC,CAACC,cAAc,CAAC,CAAC,EAAG;QAAAlB,QAAA,eAErBnJ,OAAA,CAACP,kBAAkB;UAAA8J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CACV;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAEX,CAAC,EACD;IACEX,KAAK,EAAE,SAAS;IAChBE,GAAG,EAAE,SAAS;IACdC,MAAM,EAAEA,CAACc,CAAM,EAAEC,MAAmB,kBAClCjK,OAAA,CAACnC,KAAK;MAAAsL,QAAA,GACHc,MAAM,CAACC,SAAS,gBACflK,OAAA,CAAC7B,UAAU;QACT4K,KAAK,EAAC,yFAAyF;QAC/FuB,SAAS,EAAEA,CAAA,KAAMrD,mBAAmB,CAACgD,MAAM,CAACxE,MAAM,CAAE;QACpD8E,MAAM,EAAC,KAAK;QACZC,UAAU,EAAC,IAAI;QAAArB,QAAA,eAEfnJ,OAAA,CAACpC,MAAM;UAAC4G,IAAI,EAAC,SAAS;UAAC8E,IAAI,eAAEtJ,OAAA,CAACN,cAAc;YAAA6J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACe,IAAI,EAAC,OAAO;UAAAtB,QAAA,EAAC;QAE9D;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAEb1J,OAAA,CAAC7B,UAAU;QACT4K,KAAK,EAAC,mGAAmG;QACzGuB,SAAS,EAAEA,CAAA,KAAMvD,iBAAiB,CAACkD,MAAM,CAACxE,MAAM,CAAE;QAClD8E,MAAM,EAAC,KAAK;QACZC,UAAU,EAAC,IAAI;QAAArB,QAAA,eAEfnJ,OAAA,CAACpC,MAAM;UAAC0L,IAAI,eAAEtJ,OAAA,CAACT,YAAY;YAAAgK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACe,IAAI,EAAC,OAAO;UAAAtB,QAAA,EAAC;QAE7C;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACb,eACD1J,OAAA,CAAC7B,UAAU;QACT4K,KAAK,EAAC,2FAA2F;QACjGuB,SAAS,EAAEA,CAAA,KAAMxE,kBAAkB,CAACmE,MAAM,CAACxE,MAAM,CAAE;QACnD8E,MAAM,EAAC,KAAK;QACZC,UAAU,EAAC,IAAI;QAAArB,QAAA,eAEfnJ,OAAA,CAACpC,MAAM;UAAC8M,MAAM;UAACpB,IAAI,eAAEtJ,OAAA,CAACf,kBAAkB;YAAAsK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACe,IAAI,EAAC,OAAO;UAAAtB,QAAA,EAAC;QAE1D;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,MAAMiB,cAAc,GAAG,CACrB;IACE5B,KAAK,EAAE,SAAS;IAChBC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE,SAAS;IACdC,MAAM,EAAEA,CAACL,OAAe,EAAEoB,MAAe;MAAA,IAAAW,YAAA,EAAAC,aAAA,EAAAC,iBAAA;MAAA,oBACvC9K,OAAA;QAAAmJ,QAAA,gBACEnJ,OAAA,CAACnC,KAAK;UAAAsL,QAAA,gBACJnJ,OAAA,CAAC3B,MAAM;YAAC+K,GAAG,GAAAwB,YAAA,GAAEX,MAAM,CAACvD,IAAI,cAAAkE,YAAA,uBAAXA,YAAA,CAAavB,KAAM;YAACoB,IAAI,EAAC;UAAO;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChD1J,OAAA,CAACI,IAAI;YAACuJ,MAAM;YAAAR,QAAA,EAAE,EAAA0B,aAAA,GAAAZ,MAAM,CAACvD,IAAI,cAAAmE,aAAA,uBAAXA,aAAA,CAAatC,QAAQ,KAAI;UAAc;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7D1J,OAAA,CAACI,IAAI;YAACoE,IAAI,EAAC,WAAW;YAACuG,KAAK,EAAE;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAA7B,QAAA,EAChD,IAAIW,IAAI,CAACG,MAAM,CAACgB,SAAS,CAAC,CAACZ,cAAc,CAAC;UAAC;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,EACNO,MAAM,CAAC7F,SAAS,iBACfpE,OAAA,CAAC5B,GAAG;YAAC+L,KAAK,EAAC,KAAK;YAACb,IAAI,eAAEtJ,OAAA,CAACb,cAAc;cAAAoK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAP,QAAA,EAAC;UAE3C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACR1J,OAAA;UAAK+K,KAAK,EAAE;YAAEG,SAAS,EAAE;UAAE,CAAE;UAAA/B,QAAA,EAC1Bc,MAAM,CAAC7F,SAAS,gBACfpE,OAAA;YACE+K,KAAK,EAAE;cACLI,OAAO,EAAE,UAAU;cACnBC,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,KAAK;cACnBC,SAAS,EAAE;YACb,CAAE;YAAApC,QAAA,gBAEFnJ,OAAA,CAACI,IAAI;cAACoE,IAAI,EAAC,WAAW;cAACuG,KAAK,EAAE;gBAAEZ,KAAK,EAAE;cAAU,CAAE;cAAAhB,QAAA,gBACjDnJ,OAAA,CAACb,cAAc;gBAAC4L,KAAK,EAAE;kBAAES,WAAW,EAAE;gBAAE;cAAE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAC5C,CAAAoB,iBAAA,GAAAb,MAAM,CAAC5F,SAAS,cAAAyG,iBAAA,eAAhBA,iBAAA,CAAkBW,OAAO,GACtB,mCAAmC,GACnC,kCAAkC;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,EACNO,MAAM,CAAC3F,SAAS,iBACftE,OAAA;cAAK+K,KAAK,EAAE;gBAAEG,SAAS,EAAE;cAAE,CAAE;cAAA/B,QAAA,eAC3BnJ,OAAA,CAACI,IAAI;gBACHoE,IAAI,EAAC,WAAW;gBAChBuG,KAAK,EAAE;kBAAEC,QAAQ,EAAE,MAAM;kBAAEb,KAAK,EAAE;gBAAU,CAAE;gBAAAhB,QAAA,GAC/C,aACY,EAAC,IAAIW,IAAI,CAACG,MAAM,CAAC3F,SAAS,CAAC,CAAC+F,cAAc,CAAC,CAAC;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,gBAEN1J,OAAA,CAAAE,SAAA;YAAAiJ,QAAA,gBACEnJ,OAAA,CAACI,IAAI;cAAA+I,QAAA,EAAEN,OAAO,IAAI;YAAY;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACrCO,MAAM,CAACyB,MAAM,IAAIzB,MAAM,CAACyB,MAAM,CAAC7F,MAAM,GAAG,CAAC,iBACxC7F,OAAA;cAAK+K,KAAK,EAAE;gBAAEG,SAAS,EAAE;cAAE,CAAE;cAAA/B,QAAA,EAC1Bc,MAAM,CAACyB,MAAM,CAAC1H,GAAG,CAAC,CAAC2H,GAAG,EAAEC,KAAK,kBAC5B5L,OAAA;gBAEEoJ,GAAG,EAAEuC,GAAG,CAACE,QAAS;gBAClBC,GAAG,EAAC,oBAAoB;gBACxBf,KAAK,EAAE;kBACLgB,QAAQ,EAAE,GAAG;kBACbC,SAAS,EAAE,GAAG;kBACdR,WAAW,EAAE;gBACf;cAAE,GAPGI,KAAK;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQX,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA,eACD;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;EAEV,CAAC,EACD;IACEX,KAAK,EAAE,SAAS;IAChBE,GAAG,EAAE,SAAS;IACdC,MAAM,EAAEA,CAAC+C,IAAS,EAAEhC,MAAe,KAAK;MAAA,IAAAiC,aAAA;MACtC;MACA,IAAIjC,MAAM,CAAC7F,SAAS,EAAE;QACpB,oBACEpE,OAAA,CAACI,IAAI;UAACoE,IAAI,EAAC,WAAW;UAACuG,KAAK,EAAE;YAAEQ,SAAS,EAAE;UAAS,CAAE;UAAApC,QAAA,EAAC;QAEvD;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAEX;MAEA,MAAMyC,UAAU,gBACdnM,OAAA,CAACnB,IAAI;QAAAsK,QAAA,gBACHnJ,OAAA,CAACnB,IAAI,CAACuN,IAAI;UAER9C,IAAI,eAAEtJ,OAAA,CAACb,cAAc;YAAAoK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzB2C,OAAO,EAAEA,CAAA,KAAMrE,eAAe,CAAC,QAAQ,EAAEiC,MAAM,CAAC/F,EAAE,CAAE;UAAAiF,QAAA,EACrD;QAED,GALM,QAAQ;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKH,CAAC,eACZ1J,OAAA,CAACnB,IAAI,CAACuN,IAAI;UAER9C,IAAI,eAAEtJ,OAAA,CAACf,kBAAkB;YAAAsK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7B2C,OAAO,EAAEA,CAAA,KAAMrE,eAAe,CAAC,MAAM,EAAEsE,SAAS,EAAErC,MAAM,CAACvD,IAAI,CAACxC,EAAE,CAAE;UAAAiF,QAAA,GACnE,yBACwB,EAAC,EAAA+C,aAAA,GAAAjC,MAAM,CAACvD,IAAI,cAAAwF,aAAA,uBAAXA,aAAA,CAAa3D,QAAQ,KAAI,cAAc;QAAA,GAJ3D,MAAM;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKD,CAAC,eACZ1J,OAAA,CAACnB,IAAI,CAAC0N,OAAO;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChB1J,OAAA,CAACnB,IAAI,CAACuN,IAAI;UAER9C,IAAI,eAAEtJ,OAAA,CAACV,gBAAgB;YAAAiK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3B2C,OAAO,EAAEA,CAAA,KACPrE,eAAe,CAAC,WAAW,EAAEsE,SAAS,EAAErC,MAAM,CAACvD,IAAI,CAACxC,EAAE,CACvD;UAAAiF,QAAA,EACF;QAED,GAPM,eAAe;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACP;MAED,oBACE1J,OAAA,CAACnC,KAAK;QAAAsL,QAAA,gBACJnJ,OAAA,CAAC7B,UAAU;UACT4K,KAAK,EAAC,6EAA6E;UACnFuB,SAAS,EAAEA,CAAA,KAAMnD,mBAAmB,CAAC8C,MAAM,CAAC/F,EAAE,CAAE;UAChDqG,MAAM,EAAC,KAAK;UACZC,UAAU,EAAC,IAAI;UAAArB,QAAA,eAEfnJ,OAAA,CAACpC,MAAM;YAAC8M,MAAM;YAACpB,IAAI,eAAEtJ,OAAA,CAACb,cAAc;cAAAoK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACe,IAAI,EAAC,OAAO;YAAAtB,QAAA,EAAC;UAEtD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACb1J,OAAA,CAACpB,QAAQ;UACP4N,IAAI,EAAE;YAAEC,KAAK,EAAEN,UAAU,CAACO,KAAK,CAACvD;UAAS,CAAE;UAC3CwD,OAAO,EAAE,CAAC,OAAO,CAAE;UAAAxD,QAAA,eAEnBnJ,OAAA,CAACpC,MAAM;YAAC6M,IAAI,EAAC,OAAO;YAACnB,IAAI,eAAEtJ,OAAA,CAACb,cAAc;cAAAoK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAP,QAAA,EAAC;UAE/C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAEZ;EACF,CAAC,CACF;EAED,IAAI,CAAC7I,KAAK,EAAE;IACV,oBAAOb,OAAA;MAAAmJ,QAAA,EAAK;IAAU;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC9B;EAEA,oBACE1J,OAAA;IAAK4M,SAAS,EAAC,4BAA4B;IAAAzD,QAAA,gBAEzCnJ,OAAA,CAACtC,IAAI;MAACqN,KAAK,EAAE;QAAE8B,YAAY,EAAE;MAAG,CAAE;MAAA1D,QAAA,eAChCnJ,OAAA,CAAC1B,GAAG;QAACwO,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAA5D,QAAA,eACzCnJ,OAAA,CAACzB,GAAG;UAAA4K,QAAA,eACFnJ,OAAA,CAACnC,KAAK;YAAAsL,QAAA,gBACJnJ,OAAA,CAACpC,MAAM;cACL0L,IAAI,eAAEtJ,OAAA,CAAClB,iBAAiB;gBAAAyK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC5B2C,OAAO,EAAEA,CAAA,KAAMzL,QAAQ,CAAC,mBAAmB,CAAE;cAAAuI,QAAA,EAC9C;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1J,OAAA,CAAC3B,MAAM;cAAC+K,GAAG,EAAEvI,KAAK,CAACwI,KAAM;cAACC,IAAI,eAAEtJ,OAAA,CAACX,YAAY;gBAAAkK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACe,IAAI,EAAC;YAAO;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjE1J,OAAA;cAAAmJ,QAAA,gBACEnJ,OAAA,CAACG,KAAK;gBAAC6M,KAAK,EAAE,CAAE;gBAACjC,KAAK,EAAE;kBAAEkC,MAAM,EAAE;gBAAE,CAAE;gBAAA9D,QAAA,EACnCtI,KAAK,CAACqM;cAAI;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACR1J,OAAA,CAACI,IAAI;gBAACoE,IAAI,EAAC,WAAW;gBAAA2E,QAAA,EAAC;cAAsB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP1J,OAAA,CAAC1B,GAAG;MAAC6O,MAAM,EAAE,EAAG;MAACpC,KAAK,EAAE;QAAE8B,YAAY,EAAE;MAAG,CAAE;MAAA1D,QAAA,gBAC3CnJ,OAAA,CAACzB,GAAG;QAAC6O,IAAI,EAAE,CAAE;QAAAjE,QAAA,eACXnJ,OAAA,CAACtC,IAAI;UAAAyL,QAAA,eACHnJ,OAAA,CAACxB,SAAS;YACRuK,KAAK,EAAC,eAAe;YACrBsE,KAAK,EAAEtM,OAAO,CAAC8E,MAAO;YACtByH,MAAM,eAAEtN,OAAA,CAACX,YAAY;cAAAkK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1J,OAAA,CAACzB,GAAG;QAAC6O,IAAI,EAAE,CAAE;QAAAjE,QAAA,eACXnJ,OAAA,CAACtC,IAAI;UAAAyL,QAAA,eACHnJ,OAAA,CAACxB,SAAS;YACRuK,KAAK,EAAC,gBAAgB;YACtBsE,KAAK,EAAEpM,QAAQ,CAAC4E,MAAO;YACvByH,MAAM,eAAEtN,OAAA,CAACd,eAAe;cAAAqK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1J,OAAA,CAACzB,GAAG;QAAC6O,IAAI,EAAE,CAAE;QAAAjE,QAAA,eACXnJ,OAAA,CAACtC,IAAI;UAAAyL,QAAA,eACHnJ,OAAA,CAACxB,SAAS;YACRuK,KAAK,EAAC,SAAS;YACfsE,KAAK,EAAE,IAAIvD,IAAI,CAACjJ,KAAK,CAACoK,SAAS,CAAC,CAAClB,kBAAkB,CAAC,CAAE;YACtDuD,MAAM,eAAEtN,OAAA,CAACV,gBAAgB;cAAAiK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1J,OAAA,CAACtC,IAAI;MAAAyL,QAAA,eACHnJ,OAAA,CAACvB,IAAI;QAAC8O,SAAS,EAAE1L,SAAU;QAAC2L,QAAQ,EAAE1L,YAAa;QAAAqH,QAAA,gBACjDnJ,OAAA,CAACQ,OAAO;UAACiN,GAAG,EAAE,YAAY1M,OAAO,CAAC8E,MAAM,GAAI;UAAAsD,QAAA,gBAE1CnJ,OAAA,CAAC1B,GAAG;YAAC6O,MAAM,EAAE,EAAG;YAACpC,KAAK,EAAE;cAAE8B,YAAY,EAAE;YAAG,CAAE;YAAA1D,QAAA,gBAC3CnJ,OAAA,CAACzB,GAAG;cAAC6O,IAAI,EAAE,CAAE;cAAAjE,QAAA,eACXnJ,OAAA,CAACtC,IAAI;gBAAC+M,IAAI,EAAC,OAAO;gBAAAtB,QAAA,eAChBnJ,OAAA,CAACxB,SAAS;kBACRuK,KAAK,EAAC,eAAe;kBACrBsE,KAAK,EAAEtM,OAAO,CAAC8E,MAAO;kBACtByH,MAAM,eAAEtN,OAAA,CAACX,YAAY;oBAAAkK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzBgE,UAAU,EAAE;oBAAEvD,KAAK,EAAE;kBAAU;gBAAE;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN1J,OAAA,CAACzB,GAAG;cAAC6O,IAAI,EAAE,CAAE;cAAAjE,QAAA,eACXnJ,OAAA,CAACtC,IAAI;gBAAC+M,IAAI,EAAC,OAAO;gBAAAtB,QAAA,eAChBnJ,OAAA,CAACxB,SAAS;kBACRuK,KAAK,EAAC,iBAAiB;kBACvBsE,KAAK,EAAElF,cAAc,CAACtC,MAAO;kBAC7ByH,MAAM,eAAEtN,OAAA,CAACjB,eAAe;oBAAAwK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC5BgE,UAAU,EAAE;oBAAEvD,KAAK,EAAE;kBAAU;gBAAE;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN1J,OAAA,CAACzB,GAAG;cAAC6O,IAAI,EAAE,CAAE;cAAAjE,QAAA,eACXnJ,OAAA,CAACtC,IAAI;gBAAC+M,IAAI,EAAC,OAAO;gBAAAtB,QAAA,eAChBnJ,OAAA,CAACxB,SAAS;kBACRuK,KAAK,EAAC,eAAe;kBACrBsE,KAAK,EACHtM,OAAO,CAAC0F,MAAM,CAAE6B,MAAM;oBAAA,IAAAqF,YAAA;oBAAA,OAAK,EAAAA,YAAA,GAAArF,MAAM,CAAC5B,IAAI,cAAAiH,YAAA,uBAAXA,YAAA,CAAaC,IAAI,MAAK,OAAO;kBAAA,EAAC,CACtD/H,MACJ;kBACDyH,MAAM,eAAEtN,OAAA,CAACX,YAAY;oBAAAkK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzBgE,UAAU,EAAE;oBAAEvD,KAAK,EAAE;kBAAU;gBAAE;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN1J,OAAA,CAACzB,GAAG;cAAC6O,IAAI,EAAE,CAAE;cAAAjE,QAAA,eACXnJ,OAAA,CAACtC,IAAI;gBAAC+M,IAAI,EAAC,OAAO;gBAAAtB,QAAA,eAChBnJ,OAAA,CAACxB,SAAS;kBACRuK,KAAK,EAAC,YAAY;kBAClBsE,KAAK,EACHtM,OAAO,CAAC0F,MAAM,CAAE6B,MAAM;oBAAA,IAAAuF,aAAA;oBAAA,OAAK,EAAAA,aAAA,GAAAvF,MAAM,CAAC5B,IAAI,cAAAmH,aAAA,uBAAXA,aAAA,CAAaD,IAAI,MAAK,IAAI;kBAAA,EAAC,CACnD/H,MACJ;kBACDyH,MAAM,eAAEtN,OAAA,CAACX,YAAY;oBAAAkK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzBgE,UAAU,EAAE;oBAAEvD,KAAK,EAAE;kBAAU;gBAAE;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1J,OAAA,CAAC1B,GAAG;YAAC6O,MAAM,EAAE,EAAG;YAACpC,KAAK,EAAE;cAAE8B,YAAY,EAAE;YAAG,CAAE;YAAA1D,QAAA,gBAC3CnJ,OAAA,CAACzB,GAAG;cAAC6O,IAAI,EAAE,CAAE;cAAAjE,QAAA,eACXnJ,OAAA,CAACtC,IAAI;gBAAC+M,IAAI,EAAC,OAAO;gBAAAtB,QAAA,eAChBnJ,OAAA,CAACxB,SAAS;kBACRuK,KAAK,EAAC,iBAAiB;kBACvBsE,KAAK,EAAEtM,OAAO,CAAC0F,MAAM,CAAE6B,MAAM,IAAKA,MAAM,CAAC4B,SAAS,CAAC,CAACrE,MAAO;kBAC3DyH,MAAM,eAAEtN,OAAA,CAACT,YAAY;oBAAAgK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzBgE,UAAU,EAAE;oBAAEvD,KAAK,EAAE;kBAAU;gBAAE;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN1J,OAAA,CAACzB,GAAG;cAAC6O,IAAI,EAAE,CAAE;cAAAjE,QAAA,eACXnJ,OAAA,CAACtC,IAAI;gBAAC+M,IAAI,EAAC,OAAO;gBAAAtB,QAAA,eAChBnJ,OAAA,CAACxB,SAAS;kBACRuK,KAAK,EAAC,gBAAgB;kBACtBsE,KAAK,EAAEtM,OAAO,CAAC0F,MAAM,CAAE6B,MAAM,IAAK,CAACA,MAAM,CAAC4B,SAAS,CAAC,CAACrE,MAAO;kBAC5DyH,MAAM,eAAEtN,OAAA,CAACR,mBAAmB;oBAAA+J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAChCgE,UAAU,EAAE;oBAAEvD,KAAK,EAAE;kBAAU;gBAAE;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN1J,OAAA,CAACzB,GAAG;cAAC6O,IAAI,EAAE,CAAE;cAAAjE,QAAA,eACXnJ,OAAA,CAACtC,IAAI;gBAAC+M,IAAI,EAAC,OAAO;gBAAAtB,QAAA,eAChBnJ,OAAA,CAACxB,SAAS;kBACRuK,KAAK,EAAC,YAAY;kBAClBsE,KAAK,EACHtM,OAAO,CAAC8E,MAAM,GAAG,CAAC,GACdiI,IAAI,CAACC,KAAK,CACPhN,OAAO,CAAC0F,MAAM,CAAE6B,MAAM,IAAKA,MAAM,CAAC4B,SAAS,CAAC,CAC1CrE,MAAM,GACP9E,OAAO,CAAC8E,MAAM,GACd,GACJ,CAAC,GACD,CACL;kBACDmI,MAAM,EAAC,GAAG;kBACVV,MAAM,eAAEtN,OAAA,CAACP,kBAAkB;oBAAA8J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC/BgE,UAAU,EAAE;oBAAEvD,KAAK,EAAE;kBAAU;gBAAE;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1J,OAAA;YAAK+K,KAAK,EAAE;cAAE8B,YAAY,EAAE;YAAG,CAAE;YAAA1D,QAAA,eAC/BnJ,OAAA,CAAC1B,GAAG;cAACwO,OAAO,EAAC,eAAe;cAACC,KAAK,EAAC,QAAQ;cAAA5D,QAAA,gBACzCnJ,OAAA,CAACzB,GAAG;gBAAA4K,QAAA,eACFnJ,OAAA,CAACK,MAAM;kBACL4N,WAAW,EAAC,mBAAmB;kBAC/BC,UAAU;kBACVnD,KAAK,EAAE;oBAAEoD,KAAK,EAAE;kBAAI,CAAE;kBACtBb,MAAM,eAAEtN,OAAA,CAACZ,cAAc;oBAAAmK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1J,OAAA,CAACzB,GAAG;gBAAA4K,QAAA,eACFnJ,OAAA,CAACnC,KAAK;kBAAAsL,QAAA,gBACJnJ,OAAA,CAACI,IAAI;oBAACoE,IAAI,EAAC,WAAW;oBAAA2E,QAAA,GAAC,UACb,EAACpI,OAAO,CAAC8E,MAAM,EAAC,KAAG,EAAC,GAAG,EAC9B9E,OAAO,CAAC8E,MAAM,GAAGsC,cAAc,CAACtC,MAAM,EAAC,cAC1C;kBAAA;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACP1J,OAAA,CAACpC,MAAM;oBACL4G,IAAI,EAAC,SAAS;oBACd8E,IAAI,eAAEtJ,OAAA,CAACjB,eAAe;sBAAAwK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAC1B2C,OAAO,EAAEA,CAAA,KAAM3K,0BAA0B,CAAC,IAAI,CAAE;oBAAAyH,QAAA,EACjD;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT1J,OAAA,CAACpC,MAAM;oBACL4G,IAAI,EAAC,SAAS;oBACd8E,IAAI,eAAEtJ,OAAA,CAAChB,YAAY;sBAAAuK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACvB2C,OAAO,EAAErG,qBAAsB;oBAAAmD,QAAA,EAChC;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1J,OAAA,CAACrC,KAAK;YACJyQ,OAAO,EAAEtF,aAAc;YACvBuF,UAAU,EAAEtN,OAAQ;YACpBuN,MAAM,EAAC,IAAI;YACXjN,OAAO,EAAEA,OAAQ;YACjBkN,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAG,CAAE;YAC7BC,MAAM,EAAE;cAAEC,CAAC,EAAE;YAAc,CAAE;YAC7BjE,IAAI,EAAC;UAAQ;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA,GA1I6C,SAAS;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2IjD,CAAC,eAEV1J,OAAA,CAACQ,OAAO;UAACiN,GAAG,EAAE,aAAaxM,QAAQ,CAAC4E,MAAM,GAAI;UAAAsD,QAAA,gBAC5CnJ,OAAA;YAAK+K,KAAK,EAAE;cAAE8B,YAAY,EAAE;YAAG,CAAE;YAAA1D,QAAA,eAC/BnJ,OAAA,CAAC1B,GAAG;cAACwO,OAAO,EAAC,eAAe;cAACC,KAAK,EAAC,QAAQ;cAAA5D,QAAA,gBACzCnJ,OAAA,CAACzB,GAAG;gBAAA4K,QAAA,eACFnJ,OAAA,CAACK,MAAM;kBACL4N,WAAW,EAAC,oBAAoB;kBAChCC,UAAU;kBACVb,KAAK,EAAE9L,UAAW;kBAClBiM,QAAQ,EAAGmB,CAAC,IAAKnN,aAAa,CAACmN,CAAC,CAACC,MAAM,CAACvB,KAAK,CAAE;kBAC/CtC,KAAK,EAAE;oBAAEoD,KAAK,EAAE;kBAAI,CAAE;kBACtBb,MAAM,eAAEtN,OAAA,CAACZ,cAAc;oBAAAmK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1J,OAAA,CAACzB,GAAG;gBAAA4K,QAAA,eACFnJ,OAAA,CAACnC,KAAK;kBAAAsL,QAAA,gBACJnJ,OAAA,CAAC7B,UAAU;oBACT4K,KAAK,EAAC,6HAA6H;oBACnIuB,SAAS,EAAEhD,uBAAwB;oBACnCiD,MAAM,EAAC,KAAK;oBACZC,UAAU,EAAC,IAAI;oBAAArB,QAAA,eAEfnJ,OAAA,CAACpC,MAAM;sBAAC8M,MAAM;sBAACpB,IAAI,eAAEtJ,OAAA,CAACb,cAAc;wBAAAoK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBAAAP,QAAA,EAAC;oBAEzC;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAEb1J,OAAA,CAACpC,MAAM;oBACL0L,IAAI,eAAEtJ,OAAA,CAACV,gBAAgB;sBAAAiK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAC3B2C,OAAO,EAAEA,CAAA,KAAMrE,eAAe,CAAC,WAAW,CAAE;oBAAAmB,QAAA,EAC7C;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1J,OAAA,CAACrC,KAAK;YACJyQ,OAAO,EAAEzD,cAAe;YACxB0D,UAAU,EAAE5F,gBAAiB;YAC7B6F,MAAM,EAAC,IAAI;YACXjN,OAAO,EAAEA,OAAQ;YACjBkN,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAG,CAAE;YAC7BC,MAAM,EAAE;cAAEC,CAAC,EAAE;YAAc,CAAE;YAC7BjE,IAAI,EAAC;UAAQ;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA,GA7C+C,UAAU;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8CpD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP1J,OAAA,CAAChC,KAAK;MACJ+K,KAAK,EAAC,sBAAsB;MAC5B8F,IAAI,EAAEpN,uBAAwB;MAC9BqN,QAAQ,EAAEA,CAAA,KAAMpN,0BAA0B,CAAC,KAAK,CAAE;MAClDqN,IAAI,EAAExJ,gBAAiB;MACvBgF,MAAM,EAAC,sBAAsB;MAC7B4D,KAAK,EAAE,GAAI;MAAAhF,QAAA,gBAEXnJ,OAAA;QAAK+K,KAAK,EAAE;UAAE8B,YAAY,EAAE;QAAG,CAAE;QAAA1D,QAAA,eAC/BnJ,OAAA,CAACK,MAAM;UACL4N,WAAW,EAAC,iBAAiB;UAC7BC,UAAU;UACVb,KAAK,EAAE9L,UAAW;UAClBiM,QAAQ,EAAGmB,CAAC,IAAKnN,aAAa,CAACmN,CAAC,CAACC,MAAM,CAACvB,KAAK,CAAE;UAC/CtC,KAAK,EAAE;YAAEoD,KAAK,EAAE;UAAO;QAAE;UAAA5E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN1J,OAAA,CAAC/B,MAAM;QACL+Q,IAAI,EAAC,UAAU;QACff,WAAW,EAAC,qBAAqB;QACjCZ,KAAK,EAAE1L,aAAc;QACrB6L,QAAQ,EAAE5L,gBAAiB;QAC3BmJ,KAAK,EAAE;UAAEoD,KAAK,EAAE,MAAM;UAAEtB,YAAY,EAAE;QAAG,CAAE;QAC3CoC,UAAU;QACVC,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;UAC/B,MAAMjG,QAAQ,GAAGiG,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEjG,QAAQ;UACjC,IAAI,CAACA,QAAQ,EAAE,OAAO,KAAK;UAC3B,OAAOA,QAAQ,CACZhF,QAAQ,CAAC,CAAC,CACVqE,WAAW,CAAC,CAAC,CACb9D,QAAQ,CAAC,CAAAyK,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE3G,WAAW,CAAC,CAAC,KAAI,EAAE,CAAC;QACzC,CAAE;QAAAW,QAAA,EAEDhB,cAAc,CAACnE,GAAG,CAAE0C,IAAI,iBACvB1G,OAAA,CAACM,MAAM;UAAe+M,KAAK,EAAE3G,IAAI,CAACxC,EAAG;UAAAiF,QAAA,eACnCnJ,OAAA,CAACnC,KAAK;YAAAsL,QAAA,gBACJnJ,OAAA,CAAC3B,MAAM;cAAC+K,GAAG,EAAE1C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2C,KAAM;cAACoB,IAAI,EAAC;YAAO;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACxC,CAAAhD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,QAAQ,KAAI,cAAc,EAAC,IAAE,EAAC,CAAA7B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,KAAK,KAAI,UAAU,EAAC,GACjE;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC,GAJGhD,IAAI,CAACxC,EAAE;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKZ,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAET1J,OAAA,CAACI,IAAI;QAACoE,IAAI,EAAC,WAAW;QAAA2E,QAAA,GAAExH,aAAa,CAACkE,MAAM,EAAC,mBAAiB;MAAA;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChE,CAAC,eAGR1J,OAAA,CAAChC,KAAK;MACJ+K,KAAK,EAAC,iBAAiB;MACvB8F,IAAI,EAAExM,kBAAmB;MACzByM,QAAQ,EAAEA,CAAA,KAAM;QACdxM,qBAAqB,CAAC,KAAK,CAAC;QAC5BQ,YAAY,CAAC,IAAI,CAAC;QAClBF,iBAAiB,CAAC,EAAE,CAAC;QACrBF,oBAAoB,CAAC,EAAE,CAAC;MAC1B,CAAE;MACFqM,IAAI,EAAE9G,mBAAoB;MAC1BoH,cAAc,EAAEtM,gBAAiB;MACjCwH,MAAM,EAAC,QAAQ;MACf+E,aAAa,EAAE;QAAE5E,MAAM,EAAE;MAAK,CAAE;MAAAvB,QAAA,GAE/B5G,UAAU,KAAK,QAAQ,iBACtBvC,OAAA;QAAAmJ,QAAA,EAAG;MAGH;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CACJ,EAEAnH,UAAU,KAAK,KAAK,iBACnBvC,OAAA;QAAAmJ,QAAA,EAAG;MAGH;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CACJ,EAEAnH,UAAU,KAAK,MAAM,iBACpBvC,OAAA;QAAAmJ,QAAA,EAAG;MAGH;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CACJ,EAEAnH,UAAU,KAAK,WAAW,iBACzBvC,OAAA;QAAAmJ,QAAA,gBACEnJ,OAAA;UAAAmJ,QAAA,EAAG;QAAuC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC9C1J,OAAA,CAACtB,UAAU,CAAC6B,WAAW;UACrB8M,KAAK,EAAExK,SAAU;UACjB2K,QAAQ,EAAE1K,YAAa;UACvBiI,KAAK,EAAE;YAAEoD,KAAK,EAAE,MAAM;YAAEtB,YAAY,EAAE;UAAG,CAAE;UAC3CoB,WAAW,EAAE,CAAC,YAAY,EAAE,UAAU;QAAE;UAAA1E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,EAED/G,cAAc,iBACb3C,OAAA;UAAAmJ,QAAA,gBACEnJ,OAAA;YAAAmJ,QAAA,EAAQ;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,oFAExB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACJ,EAEA,CAAC/G,cAAc,iBACd3C,OAAA;UAAAmJ,QAAA,gBACEnJ,OAAA;YAAAmJ,QAAA,EAAQ;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gFAExB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGR1J,OAAA,CAAChC,KAAK;MACJ+K,KAAK,EAAE,yBAAyB,CAAAlI,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEqM,IAAI,KAAI,OAAO,EAAG;MACzD2B,IAAI,EAAE9M,0BAA2B;MACjC+M,QAAQ,EAAEA,CAAA,KAAM;QACd9M,6BAA6B,CAAC,KAAK,CAAC;QACpCI,wBAAwB,CAAC,EAAE,CAAC;MAC9B,CAAE;MACF2M,IAAI,EAAEjI,uBAAwB;MAC9ByD,MAAM,EAAC,oBAAoB;MAC3B+E,aAAa,EAAE;QAAEC,QAAQ,EAAEpN,qBAAqB,CAAC0D,MAAM,KAAK;MAAE,CAAE;MAChEsI,KAAK,EAAE,GAAI;MAAAhF,QAAA,gBAEXnJ,OAAA;QAAK+K,KAAK,EAAE;UAAE8B,YAAY,EAAE;QAAG,CAAE;QAAA1D,QAAA,eAC/BnJ,OAAA,CAACI,IAAI;UAACoE,IAAI,EAAC,WAAW;UAAA2E,QAAA,gBACpBnJ,OAAA,CAACP,kBAAkB;YAACsL,KAAK,EAAE;cAAES,WAAW,EAAE;YAAE;UAAE;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,8GAGnD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAEL7I,KAAK,IAAIA,KAAK,CAACyF,OAAO,IAAIzF,KAAK,CAACyF,OAAO,CAACT,MAAM,GAAG,CAAC,iBACjD7F,OAAA;QACE+K,KAAK,EAAE;UACL8B,YAAY,EAAE,EAAE;UAChB1B,OAAO,EAAE,EAAE;UACXC,eAAe,EAAE,SAAS;UAC1BE,YAAY,EAAE,CAAC;UACfD,MAAM,EAAE;QACV,CAAE;QAAAlC,QAAA,gBAEFnJ,OAAA,CAACI,IAAI;UAACuJ,MAAM;UAACoB,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAG,CAAE;UAAA7B,QAAA,GAAC,gBACtB,EAAC,GAAG,EACjBtI,KAAK,CAACyF,OAAO,CAACtC,GAAG,CAAEuC,CAAM,IAAKA,CAAC,CAACiJ,OAAO,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QAAA;UAAAlG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACP1J,OAAA;UAAAuJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN1J,OAAA,CAACI,IAAI;UAACoE,IAAI,EAAC,WAAW;UAACuG,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAG,CAAE;UAAA7B,QAAA,EAAC;QAGhD;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,eAED1J,OAAA,CAAC/B,MAAM;QACL+Q,IAAI,EAAC,UAAU;QACff,WAAW,EAAC,8BAA8B;QAC1ClD,KAAK,EAAE;UAAEoD,KAAK,EAAE;QAAO,CAAE;QACzBd,KAAK,EAAElL,qBAAsB;QAC7BqL,QAAQ,EAAEpL,wBAAyB;QACnC6M,UAAU;QACVC,YAAY,EAAEA,CAACC,KAAK,EAAEC,MAAM;UAAA,IAAAM,aAAA;UAAA,OAC1B,EAAAA,aAAA,GAACN,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEO,KAAK,cAAAD,aAAA,cAAAA,aAAA,GAAI,EAAE,EAAElH,WAAW,CAAC,CAAC,CAAC9D,QAAQ,CAACyK,KAAK,CAAC3G,WAAW,CAAC,CAAC,CAAC;QAAA,CAClE;QACDoH,OAAO,EAAE3N,aAAa,CAAC+B,GAAG,CAAE0C,IAAI,KAAM;UACpC2G,KAAK,EAAE3G,IAAI,CAACxC,EAAE,CAACC,QAAQ,CAAC,CAAC;UACzBwL,KAAK,EAAE,GAAGjJ,IAAI,CAAC6B,QAAQ,KAAK7B,IAAI,CAACkD,KAAK,OAAOlD,IAAI,CAACkH,IAAI;QACxD,CAAC,CAAC;MAAE;QAAArE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAEDvH,qBAAqB,CAAC0D,MAAM,GAAG,CAAC,iBAC/B7F,OAAA;QACE+K,KAAK,EAAE;UACLG,SAAS,EAAE,EAAE;UACbC,OAAO,EAAE,EAAE;UACXC,eAAe,EAAE,SAAS;UAC1BE,YAAY,EAAE,CAAC;UACfD,MAAM,EAAE;QACV,CAAE;QAAAlC,QAAA,eAEFnJ,OAAA,CAACI,IAAI;UAACuJ,MAAM;UAACoB,KAAK,EAAE;YAAEZ,KAAK,EAAE;UAAU,CAAE;UAAAhB,QAAA,GACtChH,qBAAqB,CAAC0D,MAAM,EAAC,gCAChC;QAAA;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAChJ,EAAA,CAzjCID,YAAsB;EAAA,QACNd,SAAS,EACZC,WAAW;AAAA;AAAAiQ,EAAA,GAFxBpP,YAAsB;AA2jC5B,eAAeA,YAAY;AAAC,IAAAoP,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}