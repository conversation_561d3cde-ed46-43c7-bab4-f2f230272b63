import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Linking,
  Alert,
} from 'react-native';
import {NavigationProp} from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import AppTextInput from '../../components/AppTextInput';
import normalize from '../../types/utiles';
import Font from '../../constants/Font';
import authService from '../../services/authService';
import {ALERT_TYPE, Dialog} from '../../components/CustomAlert';

type Props = {
  navigation: NavigationProp<any>;
};

const ForgotPasswordScreen: React.FC<Props> = ({navigation}) => {
  const [email, setEmail] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [emailSent, setEmailSent] = useState<boolean>(false);

  const handleForgotPassword = async () => {
    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

    if (!email) {
      Dialog.show({
        type: ALERT_TYPE.DANGER,
        title: 'Error',
        textBody: 'Please enter your email address',
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
      return;
    }

    if (!emailPattern.test(email)) {
      Dialog.show({
        type: ALERT_TYPE.DANGER,
        title: 'Error',
        textBody: 'Please enter a valid email address',
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
      return;
    }

    setLoading(true);

    try {
      const data = await authService.forgotPassword(email);

      if (data.success) {
        setEmailSent(true);
        Dialog.show({
          type: ALERT_TYPE.SUCCESS,
          title: 'Email Sent',
          textBody:
            'Password reset instructions have been sent to your email address.',
          button: 'OK',
          onPressButton: () => Dialog.hide(),
        });
      } else {
        Dialog.show({
          type: ALERT_TYPE.DANGER,
          title: 'Error',
          textBody:
            data.message || 'Failed to send reset email. Please try again.',
          button: 'OK',
          onPressButton: () => Dialog.hide(),
        });
      }
    } catch (error) {
      console.error('Forgot password error:', error);
      Dialog.show({
        type: ALERT_TYPE.DANGER,
        title: 'Error',
        textBody: 'Network error. Please check your connection and try again.',
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
    } finally {
      setLoading(false);
    }
  };

  const handleResendEmail = () => {
    setEmailSent(false);
    handleForgotPassword();
  };

  const handleOpenWebApp = () => {
    Alert.alert(
      'Reset Password Options',
      'Choose how you would like to reset your password:',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Mobile App',
          onPress: () => {
            Dialog.show({
              type: ALERT_TYPE.SUCCESS,
              title: 'Check Your Email',
              textBody:
                'Please check your email and click "Open in Mobile App" to reset your password in this app.',
              button: 'OK',
              onPressButton: () => Dialog.hide(),
            });
          },
        },
        {
          text: 'Web Browser',
          onPress: () => {
            const webUrl = `${
              process.env.REACT_APP_FRONTEND_URL || 'http://192.168.1.90:3000'
            }/forgot-password?fromMobile=true`;
            Linking.openURL(webUrl).catch(() => {
              Dialog.show({
                type: ALERT_TYPE.DANGER,
                title: 'Error',
                textBody:
                  'Unable to open web browser. Please check your email and click "Open in Browser" to reset your password on the web.',
                button: 'OK',
                onPressButton: () => Dialog.hide(),
              });
            });
          },
        },
      ],
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#002157" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Forgot Password</Text>
        <View style={styles.headerRight} />
      </View>

      <View style={styles.content}>
        {!emailSent ? (
          <>
            {/* Title and Description */}
            <View style={styles.titleContainer}>
              <Text style={styles.title}>Reset Your Password</Text>
              <Text style={styles.description}>
                Enter your email address and we'll send you instructions to
                reset your password.
              </Text>
            </View>

            {/* Email Input */}
            <View style={styles.inputContainer}>
              <View style={styles.inputWrapper}>
                <Ionicons
                  name="mail-outline"
                  size={20}
                  color="#3795BD"
                  style={styles.inputIcon}
                />
                <AppTextInput
                  placeholder="Enter your email address"
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  style={styles.textInput}
                />
              </View>
            </View>

            {/* Send Reset Email Button */}
            <TouchableOpacity
              style={[styles.resetButton, loading && styles.disabledButton]}
              onPress={handleForgotPassword}
              disabled={loading}>
              {loading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text style={styles.resetButtonText}>Send Reset Email</Text>
              )}
            </TouchableOpacity>

            {/* Quick Access to Web App */}
            <View style={styles.quickAccessContainer}>
              <Text style={styles.quickAccessText}>
                Or reset password directly:
              </Text>
              <TouchableOpacity
                style={styles.quickWebButton}
                onPress={() => {
                  const webUrl = `${
                    process.env.REACT_APP_FRONTEND_URL ||
                    'http://192.168.1.90:3000'
                  }/forgot-password?fromMobile=true`;
                  Linking.openURL(webUrl).catch(() => {
                    Dialog.show({
                      type: ALERT_TYPE.DANGER,
                      title: 'Error',
                      textBody:
                        'Unable to open web browser. Please try sending reset instructions via email instead.',
                      button: 'OK',
                      onPressButton: () => Dialog.hide(),
                    });
                  });
                }}>
                <Ionicons
                  name="globe"
                  size={16}
                  color="#666"
                  style={styles.quickButtonIcon}
                />
                <Text style={styles.quickWebButtonText}>
                  Open in Web Browser
                </Text>
              </TouchableOpacity>
            </View>
          </>
        ) : (
          <>
            {/* Email Sent Success State */}
            <View style={styles.successContainer}>
              <View style={styles.successIconContainer}>
                <Ionicons name="checkmark-circle" size={80} color="#4CAF50" />
              </View>
              <Text style={styles.successTitle}>Email Sent!</Text>
              <Text style={styles.successDescription}>
                We've sent password reset instructions to:
              </Text>
              <Text style={styles.emailText}>{email}</Text>
              <Text style={styles.successNote}>
                Please check your email and follow the instructions to reset
                your password. The link will expire in 1 hour.
              </Text>
            </View>

            {/* Reset Options */}
            <View style={styles.resetOptionsContainer}>
              <Text style={styles.resetOptionsTitle}>
                Reset Password Options:
              </Text>

              <TouchableOpacity
                style={styles.mobileAppButton}
                onPress={() => {
                  Dialog.show({
                    type: ALERT_TYPE.SUCCESS,
                    title: 'Check Your Email',
                    textBody:
                      'Please check your email and click "Open in Mobile App" to reset your password in this app.',
                    button: 'OK',
                    onPressButton: () => Dialog.hide(),
                  });
                }}>
                <Ionicons
                  name="phone-portrait"
                  size={20}
                  color="#fff"
                  style={styles.buttonIcon}
                />
                <Text style={styles.mobileAppButtonText}>
                  Reset in Mobile App
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.webAppButton}
                onPress={() => {
                  const webUrl = `${
                    process.env.REACT_APP_FRONTEND_URL ||
                    'http://192.168.1.90:3000'
                  }/forgot-password?fromMobile=true`;
                  Linking.openURL(webUrl).catch(() => {
                    Dialog.show({
                      type: ALERT_TYPE.DANGER,
                      title: 'Error',
                      textBody:
                        'Unable to open web browser. Please check your email and click "Open in Browser" to reset your password on the web.',
                      button: 'OK',
                      onPressButton: () => Dialog.hide(),
                    });
                  });
                }}>
                <Ionicons
                  name="globe"
                  size={20}
                  color="#fff"
                  style={styles.buttonIcon}
                />
                <Text style={styles.webAppButtonText}>
                  Reset in Web Browser
                </Text>
              </TouchableOpacity>
            </View>

            {/* Resend Email Button */}
            <TouchableOpacity
              style={styles.resendButton}
              onPress={handleResendEmail}>
              <Text style={styles.resendButtonText}>Resend Email</Text>
            </TouchableOpacity>
          </>
        )}

        {/* Back to Login */}
        <TouchableOpacity
          style={styles.backToLoginButton}
          onPress={() => navigation.navigate('Login')}>
          <Ionicons name="arrow-back" size={16} color="#3795BD" />
          <Text style={styles.backToLoginText}>Back to Login</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#002157',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: StatusBar.currentHeight ? StatusBar.currentHeight + 12 : 12,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: normalize(20),
    paddingTop: normalize(40),
  },
  titleContainer: {
    marginBottom: normalize(40),
    alignItems: 'center',
  },
  title: {
    fontSize: normalize(28),
    fontWeight: '700',
    color: '#002157',
    marginBottom: normalize(12),
    textAlign: 'center',
    fontFamily: Font['poppins-bold'],
  },
  description: {
    fontSize: normalize(16),
    color: '#666',
    textAlign: 'center',
    lineHeight: normalize(24),
    fontFamily: Font['poppins-regular'],
  },
  inputContainer: {
    marginBottom: normalize(30),
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#3795BD',
    backgroundColor: '#fff',
    borderRadius: normalize(8),
    paddingHorizontal: normalize(16),
    height: normalize(50),
  },
  inputIcon: {
    marginRight: normalize(12),
  },
  textInput: {
    flex: 1,
    color: '#000',
    fontSize: normalize(16),
    fontFamily: Font['poppins-regular'],
  },
  resetButton: {
    backgroundColor: '#3795BD',
    borderRadius: normalize(8),
    height: normalize(50),
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: normalize(20),
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  resetButtonText: {
    color: '#fff',
    fontSize: normalize(16),
    fontWeight: '600',
    fontFamily: Font['poppins-bold'],
  },
  successContainer: {
    alignItems: 'center',
    marginBottom: normalize(40),
  },
  successIconContainer: {
    marginBottom: normalize(20),
  },
  successTitle: {
    fontSize: normalize(24),
    fontWeight: '700',
    color: '#4CAF50',
    marginBottom: normalize(12),
    fontFamily: Font['poppins-bold'],
  },
  successDescription: {
    fontSize: normalize(16),
    color: '#666',
    textAlign: 'center',
    marginBottom: normalize(8),
    fontFamily: Font['poppins-regular'],
  },
  emailText: {
    fontSize: normalize(16),
    fontWeight: '600',
    color: '#002157',
    marginBottom: normalize(16),
    fontFamily: Font['poppins-bold'],
  },
  successNote: {
    fontSize: normalize(14),
    color: '#666',
    textAlign: 'center',
    lineHeight: normalize(20),
    fontFamily: Font['poppins-regular'],
  },
  resendButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#3795BD',
    borderRadius: normalize(8),
    height: normalize(50),
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: normalize(20),
  },
  resendButtonText: {
    color: '#3795BD',
    fontSize: normalize(16),
    fontWeight: '600',
    fontFamily: Font['poppins-bold'],
  },
  backToLoginButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: normalize(20),
  },
  backToLoginText: {
    color: '#3795BD',
    fontSize: normalize(16),
    fontWeight: '500',
    marginLeft: normalize(8),
    fontFamily: Font['poppins-regular'],
  },
  resetOptionsContainer: {
    marginTop: normalize(25),
    marginBottom: normalize(15),
  },
  resetOptionsTitle: {
    fontSize: normalize(16),
    fontFamily: Font['poppins-medium'],
    color: '#333',
    textAlign: 'center',
    marginBottom: normalize(15),
  },
  mobileAppButton: {
    backgroundColor: '#3795BD',
    paddingVertical: normalize(12),
    paddingHorizontal: normalize(20),
    borderRadius: normalize(8),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: normalize(10),
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  mobileAppButtonText: {
    color: '#fff',
    fontSize: normalize(14),
    fontFamily: Font['poppins-medium'],
    marginLeft: normalize(8),
  },
  webAppButton: {
    backgroundColor: '#666',
    paddingVertical: normalize(12),
    paddingHorizontal: normalize(20),
    borderRadius: normalize(8),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: normalize(10),
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  webAppButtonText: {
    color: '#fff',
    fontSize: normalize(14),
    fontFamily: Font['poppins-medium'],
    marginLeft: normalize(8),
  },
  buttonIcon: {
    marginRight: normalize(5),
  },
  quickAccessContainer: {
    marginTop: normalize(20),
    alignItems: 'center',
  },
  quickAccessText: {
    fontSize: normalize(14),
    color: '#666',
    fontFamily: Font['poppins-regular'],
    marginBottom: normalize(10),
  },
  quickWebButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: normalize(8),
    paddingHorizontal: normalize(15),
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: normalize(6),
    backgroundColor: '#f8f9fa',
  },
  quickWebButtonText: {
    color: '#666',
    fontSize: normalize(13),
    fontFamily: Font['poppins-regular'],
    marginLeft: normalize(6),
  },
  quickButtonIcon: {
    marginRight: normalize(4),
  },
});

export default ForgotPasswordScreen;
